#!/usr/bin/env python3
"""
Create a test user for testing login and save recipe functionality
"""

import requests
import json

# Test configuration
BASE_URL = "http://127.0.0.1:5000"
TEST_USER = {
    "name": "Test User",
    "email": "<EMAIL>",
    "password": "password123"
}

def create_test_user():
    """Create a test user via signup API"""
    print("👤 Creating test user...")
    
    signup_url = f"{BASE_URL}/api/auth/signup"
    
    try:
        response = requests.post(signup_url, json=TEST_USER, timeout=10)
        print(f"Signup status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Test user created successfully!")
            print(f"User: {data.get('user', {}).get('name', 'Unknown')}")
            return True
        elif response.status_code == 409:
            print("ℹ️  Test user already exists")
            return True
        else:
            print(f"❌ Signup failed: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection refused - server not running")
        return False
    except Exception as e:
        print(f"❌ Signup error: {e}")
        return False

def test_login():
    """Test login with the test user"""
    print("\n🔐 Testing login with test user...")
    
    login_url = f"{BASE_URL}/api/auth/login"
    
    try:
        response = requests.post(login_url, json={
            "email": TEST_USER["email"],
            "password": TEST_USER["password"]
        }, timeout=10)
        print(f"Login status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Login successful!")
            print(f"User: {data.get('user', {}).get('name', 'Unknown')}")
            print(f"Token: {data.get('token', 'No token')[:50]}...")
            return data.get('token')
        else:
            print(f"❌ Login failed: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Login error: {e}")
        return None

def main():
    """Main function"""
    print("=" * 60)
    print("🧪 CREATING TEST USER FOR SISARASA")
    print("=" * 60)
    
    # Create test user
    if create_test_user():
        # Test login
        token = test_login()
        
        if token:
            print("\n🎉 Test user setup complete! Ready for testing.")
        else:
            print("\n❌ Test user created but login failed")
    else:
        print("\n❌ Failed to create test user")

if __name__ == "__main__":
    main()
