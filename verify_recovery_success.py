#!/usr/bin/env python3
"""
Quick verification script to confirm the review recovery was successful
"""

import os
import sys
from datetime import datetime
from pymongo import MongoClient
from collections import Counter

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def load_atlas_config():
    """Load MongoDB Atlas connection string."""
    try:
        env_path = '.env'
        if os.path.exists(env_path):
            with open(env_path, 'r') as f:
                for line in f:
                    if line.startswith('MONGO_URI='):
                        return line.split('=', 1)[1].strip().strip('"\'')
        return os.getenv('MONGO_URI')
    except Exception as e:
        print(f"❌ Error loading Atlas configuration: {e}")
        return None

def verify_recovery():
    """Verify the recovery was successful."""
    print("🔍 VERIFYING REVIEW RECOVERY SUCCESS")
    print("=" * 50)
    
    try:
        atlas_uri = load_atlas_config()
        if not atlas_uri:
            print("❌ Could not load database configuration")
            return False
        
        client = MongoClient(atlas_uri, serverSelectionTimeoutMS=10000)
        db = client['sisarasa']
        
        # Check collections
        collections = db.list_collection_names()
        print(f"📦 Available collections: {len(collections)}")
        
        # Check recipe_reviews collection
        if 'recipe_reviews' in collections:
            review_count = db.recipe_reviews.count_documents({})
            print(f"✅ recipe_reviews collection: {review_count:,} reviews")
            
            # Sample review structure
            sample_review = db.recipe_reviews.find_one({})
            if sample_review:
                print(f"📋 Sample review structure:")
                for key, value in sample_review.items():
                    if key == '_id':
                        continue
                    print(f"   {key}: {type(value).__name__}")
        else:
            print("❌ recipe_reviews collection not found")
            return False
        
        # Check review_votes collection (should be empty or much smaller)
        if 'review_votes' in collections:
            vote_count = db.review_votes.count_documents({})
            print(f"🗳️  review_votes collection: {vote_count:,} votes")
        else:
            print("✅ review_votes collection removed")
        
        # Check recipes and users
        recipe_count = db.recipes.count_documents({})
        user_count = db.users.count_documents({})
        
        print(f"🍳 Total recipes: {recipe_count:,}")
        print(f"👥 Total users: {user_count:,}")
        
        # Calculate ratio
        if recipe_count > 0:
            ratio = review_count / recipe_count
            print(f"📊 Review-to-recipe ratio: {ratio:.2f}")
            
            if 3 <= ratio <= 5:
                print("✅ EXCELLENT: Optimal ratio for recommendations")
            elif ratio > 5:
                print("✅ GOOD: High engagement ratio")
            else:
                print("⚠️  LOW: May need more reviews")
        
        # Check rating distribution
        print(f"\n📊 RATING DISTRIBUTION:")
        rating_pipeline = [
            {'$group': {'_id': '$rating', 'count': {'$sum': 1}}},
            {'$sort': {'_id': 1}}
        ]
        
        rating_dist = list(db.recipe_reviews.aggregate(rating_pipeline))
        for item in rating_dist:
            stars = '⭐' * item['_id']
            percentage = (item['count'] / review_count) * 100
            print(f"   {item['_id']} star {stars}: {item['count']:,} ({percentage:.1f}%)")
        
        # Check recipes with reviews
        recipes_with_reviews = len(db.recipe_reviews.distinct('recipe_id'))
        coverage = (recipes_with_reviews / recipe_count) * 100
        print(f"\n📈 RECIPE COVERAGE:")
        print(f"   Recipes with reviews: {recipes_with_reviews:,} / {recipe_count:,} ({coverage:.1f}%)")
        
        client.close()
        
        print(f"\n✅ RECOVERY VERIFICATION COMPLETE")
        print(f"🎯 Your review system is ready for recommendations!")
        
        return True
        
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        return False

if __name__ == "__main__":
    print("SisaRasa Review Recovery Verification")
    print("-" * 40)
    verify_recovery()
