#!/usr/bin/env python3
"""
Test the debug MongoDB endpoint
"""

import requests
import json

def test_debug_endpoint():
    """Test the debug endpoint"""
    print("🔍 Testing Debug Endpoint...")
    print("=" * 50)
    
    try:
        response = requests.get("http://localhost:5000/api/auth/debug-mongo", timeout=10)
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"📊 Response: {json.dumps(data, indent=2)}")
            return True
        else:
            print(f"❌ Error Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

if __name__ == "__main__":
    test_debug_endpoint()
