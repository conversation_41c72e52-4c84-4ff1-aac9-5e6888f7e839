#!/usr/bin/env python3
"""
Test review submission functionality for SisaRasa
"""

import os
import sys
import requests
import json
from dotenv import load_dotenv

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_review_submission():
    """Test the review submission API endpoint."""
    print("🔍 Testing Review Submission Functionality")
    print("=" * 50)
    
    # Load environment variables
    load_dotenv()
    
    # Test configuration
    base_url = "http://localhost:5000"
    
    # Test data
    test_user_email = "<EMAIL>"
    test_user_password = "password123"
    test_recipe_id = "674b123456789012345678ab"  # Example recipe ID
    
    print("🔄 Step 1: Testing user authentication...")
    
    # First, try to login to get a JWT token
    login_data = {
        "email": test_user_email,
        "password": test_user_password
    }
    
    try:
        login_response = requests.post(
            f"{base_url}/api/auth/login",
            json=login_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        if login_response.status_code == 200:
            login_result = login_response.json()
            if login_result.get('status') == 'success':
                token = login_result.get('access_token')
                print("✅ User authentication successful")
            else:
                print(f"❌ Login failed: {login_result.get('message', 'Unknown error')}")
                return False
        else:
            print(f"❌ Login request failed with status {login_response.status_code}")
            print("Creating a test user first...")
            
            # Try to create a test user
            signup_data = {
                "name": "Test User",
                "email": test_user_email,
                "password": test_user_password
            }
            
            signup_response = requests.post(
                f"{base_url}/api/auth/signup",
                json=signup_data,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            if signup_response.status_code == 201:
                print("✅ Test user created successfully")
                # Try login again
                login_response = requests.post(
                    f"{base_url}/api/auth/login",
                    json=login_data,
                    headers={'Content-Type': 'application/json'},
                    timeout=10
                )
                
                if login_response.status_code == 200:
                    login_result = login_response.json()
                    token = login_result.get('access_token')
                    print("✅ User authentication successful after signup")
                else:
                    print("❌ Login failed after signup")
                    return False
            else:
                print(f"❌ User creation failed: {signup_response.text}")
                return False
    
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to the server. Is the Flask app running?")
        print("Please start the server with: python src/simple_app.py")
        return False
    except Exception as e:
        print(f"❌ Authentication error: {e}")
        return False
    
    print("\n🔄 Step 2: Testing review submission...")
    
    # Test review submission
    review_data = {
        "rating": 5,
        "review_text": "This is a test review for database connectivity testing."
    }
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        # First, get a valid recipe ID from the database
        print("🔍 Finding a valid recipe ID...")
        
        # Import database connection
        from pymongo import MongoClient
        mongo_uri = os.getenv('MONGO_URI')
        client = MongoClient(mongo_uri)
        db = client['sisarasa']
        
        # Get a recipe ID
        recipe = db.recipes.find_one({})
        if recipe:
            test_recipe_id = str(recipe['_id'])
            print(f"✅ Using recipe ID: {test_recipe_id}")
        else:
            print("❌ No recipes found in database")
            return False
        
        client.close()
        
        # Submit review
        review_response = requests.post(
            f"{base_url}/api/community/recipes/{test_recipe_id}/reviews",
            json=review_data,
            headers=headers,
            timeout=10
        )
        
        print(f"📤 Review submission response status: {review_response.status_code}")
        print(f"📤 Review submission response: {review_response.text}")
        
        if review_response.status_code in [200, 201]:
            review_result = review_response.json()
            if review_result.get('status') == 'success':
                print("✅ Review submission successful!")
                print(f"   Review ID: {review_result.get('review_id', review_result.get('review', {}).get('id', 'N/A'))}")
                return True
            else:
                print(f"❌ Review submission failed: {review_result.get('message', 'Unknown error')}")
                return False
        else:
            print(f"❌ Review submission failed with status {review_response.status_code}")
            try:
                error_data = review_response.json()
                print(f"   Error: {error_data.get('message', 'Unknown error')}")
            except:
                print(f"   Raw response: {review_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Review submission error: {e}")
        return False

if __name__ == "__main__":
    success = test_review_submission()
    if success:
        print("\n🎯 Review submission test PASSED")
    else:
        print("\n❌ Review submission test FAILED")
