# MongoDB Connection - Use MongoDB Atlas for production
# MONGO_URI=mongodb+srv://<username>:<password>@<cluster>.mongodb.net/sisarasa?retryWrites=true&w=majority
# For local development only (not recommended for production):
# MONGO_URI=mongodb://localhost:27017/sisarasa

# JWT Secret Key (generate a secure random key for production)
JWT_SECRET_KEY=your-secret-key-change-this-in-production
JWT_ACCESS_TOKEN_EXPIRES=86400  # 24 hours in seconds

# Email Configuration (for password reset emails)
# SMTP server settings
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USE_SSL=False
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_DEFAULT_SENDER=<EMAIL>
