# MongoDB Migration Guide: Local to Atlas

This guide provides comprehensive tools and instructions for migrating your SisaRasa application data from a local MongoDB database to MongoDB Atlas.

## 📋 Overview

The migration process includes:
- **Data Migration**: Transfer all collections from local MongoDB to Atlas
- **Data Verification**: Comprehensive checks to ensure migration success
- **Rollback Options**: Safety mechanisms to revert changes if needed
- **Detailed Logging**: Complete audit trail of the migration process

## 🛠️ Migration Tools

### 1. `migrate_to_atlas.py` - Main Migration Script
- Migrates all collections from local MongoDB to Atlas
- Preserves ObjectId references and relationships
- Handles duplicate data conflicts intelligently
- Provides detailed progress reporting
- Safe to run multiple times (idempotent)

### 2. `verify_migration.py` - Verification Script
- Compares data between local and Atlas databases
- Verifies data integrity and relationships
- Checks index migration
- Generates detailed verification reports

### 3. `rollback_migration.py` - Rollback Tool
- Creates backups of Atlas data
- Provides rollback capabilities
- Shows database status
- Emergency data recovery options

## 🚀 Quick Start

### Prerequisites
1. Ensure your local MongoDB is running on `mongodb://localhost:27017/`
2. Verify your `.env` file contains the correct `MONGO_URI` for Atlas
3. Install required dependencies: `pip install pymongo`

### Step 1: Run the Migration
```bash
python migrate_to_atlas.py
```

The script will:
- Connect to both local and Atlas databases
- Discover all collections in your local database
- Migrate data in batches with progress updates
- Handle conflicts and duplicates automatically
- Provide a detailed summary

### Step 2: Verify the Migration
```bash
python verify_migration.py
```

This will:
- Compare document counts between databases
- Verify user data integrity
- Check data relationships
- Validate index migration
- Generate a verification report

### Step 3: Test Your Application
After migration, test your SisaRasa application to ensure:
- User login works correctly
- Saved recipes are preserved
- Search history is intact
- Community features function properly

## 📊 Expected Collections

The migration will handle these collections:
- **users**: User accounts, saved recipes, search history
- **recipes**: Custom recipes (if any)
- **reviews**: User reviews and ratings
- **community_posts**: Community discussions
- **community_recipes**: User-shared recipes
- **verifications**: Recipe verifications
- **post_comments**: Comments on community posts
- **recipe_ratings**: Recipe rating data

## 🔧 Advanced Usage

### Custom Collection Migration
To migrate specific collections only:
```python
# Edit migrate_to_atlas.py
self.collections_to_migrate = ['users', 'reviews']  # Specify collections
```

### Handling Conflicts
The migration script handles conflicts by:
- **Users**: Updates if newer data is found
- **Other collections**: Updates if more complete data is found
- **Duplicates**: Skips with detailed logging

### Batch Size Adjustment
For large datasets, adjust batch size:
```python
# In migrate_to_atlas.py
batch_size = 50  # Reduce for slower connections
```

## 🛡️ Safety Features

### Automatic Backups
The migration creates automatic backups before making changes.

### Idempotent Operations
Safe to run multiple times - won't create duplicates.

### Detailed Logging
Every operation is logged with timestamps and details.

### Rollback Capability
```bash
# Create backup before migration
python rollback_migration.py backup

# Check Atlas status
python rollback_migration.py status

# Emergency rollback (clears Atlas data)
python rollback_migration.py clear

# Restore from backup
python rollback_migration.py restore:backup_directory_name
```

## 📈 Migration Process Flow

```
1. Load Configuration
   ├── Read .env file for Atlas URI
   └── Validate connection strings

2. Connect to Databases
   ├── Test local MongoDB connection
   └── Test Atlas connection

3. Discover Collections
   ├── List all collections in local DB
   └── Count documents in each collection

4. Migrate Collections
   ├── Create indexes in Atlas
   ├── Migrate documents in batches
   ├── Handle conflicts and duplicates
   └── Track migration statistics

5. Verify Migration
   ├── Compare document counts
   ├── Verify data integrity
   └── Check relationships

6. Generate Reports
   ├── Migration summary
   └── Verification results
```

## 🔍 Troubleshooting

### Connection Issues
```bash
# Test local MongoDB
mongo --eval "db.adminCommand('ping')"

# Test Atlas connection (check .env file)
python -c "from pymongo import MongoClient; print(MongoClient('your_atlas_uri').admin.command('ping'))"
```

### Memory Issues
For large datasets:
- Reduce batch size in migration script
- Run migration during off-peak hours
- Monitor system resources

### Partial Migration
If migration fails partway:
- Check the detailed logs for specific errors
- Fix any connection or permission issues
- Re-run the migration (it will skip already migrated data)

### Data Verification Failures
If verification shows missing data:
- Check for connection timeouts during migration
- Verify local database is still accessible
- Re-run migration for specific collections

## 📝 Migration Checklist

### Before Migration
- [ ] Local MongoDB is running and accessible
- [ ] Atlas cluster is created and accessible
- [ ] `.env` file contains correct Atlas URI
- [ ] Application is stopped to prevent data changes
- [ ] Backup of local database created (optional)

### During Migration
- [ ] Monitor migration progress
- [ ] Check for error messages
- [ ] Verify network connectivity remains stable

### After Migration
- [ ] Run verification script
- [ ] Test application functionality
- [ ] Check user login and data access
- [ ] Verify saved recipes and search history
- [ ] Test community features
- [ ] Update application configuration if needed

### Cleanup (Optional)
- [ ] Archive local database
- [ ] Remove migration scripts from production
- [ ] Update documentation

## 🆘 Emergency Procedures

### If Migration Fails
1. Check error logs in migration output
2. Verify database connections
3. Fix any identified issues
4. Re-run migration (safe to repeat)

### If Application Breaks After Migration
1. Check application logs for connection errors
2. Verify `.env` file configuration
3. Test Atlas connectivity
4. Use rollback script if necessary:
   ```bash
   python rollback_migration.py clear
   ```

### If Data is Missing
1. Run verification script to identify gaps
2. Check migration logs for skipped documents
3. Re-run migration for specific collections
4. Contact support if issues persist

## 📞 Support

If you encounter issues:
1. Check the detailed logs generated by each script
2. Verify your Atlas cluster configuration
3. Ensure network connectivity to Atlas
4. Review the troubleshooting section above

The migration tools are designed to be robust and handle most common scenarios automatically. The detailed logging will help identify and resolve any issues that arise.
