#!/usr/bin/env python3
"""
SisaRasa Database Structure Inspector
====================================

This script inspects the actual database structure to understand
how reviews and ratings are stored in your SisaRasa system.
"""

import os
import sys
from datetime import datetime
from pymongo import MongoClient
from bson import ObjectId
import json
from collections import defaultdict, Counter

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

class DatabaseInspector:
    def __init__(self):
        """Initialize the database inspector."""
        self.atlas_uri = None
        self.atlas_db_name = "sisarasa"
        self.atlas_client = None
        self.atlas_db = None

    def load_atlas_config(self):
        """Load MongoDB Atlas connection string."""
        try:
            env_path = '.env'
            if os.path.exists(env_path):
                with open(env_path, 'r') as f:
                    for line in f:
                        if line.startswith('MONGO_URI='):
                            self.atlas_uri = line.split('=', 1)[1].strip().strip('"\'')
                            break
            
            if not self.atlas_uri:
                self.atlas_uri = os.getenv('MONGO_URI')
            
            if not self.atlas_uri:
                raise ValueError("MongoDB Atlas URI not found")
                
            return True
            
        except Exception as e:
            print(f"❌ Error loading Atlas configuration: {e}")
            return False

    def connect_database(self):
        """Connect to Atlas database."""
        try:
            print("🔗 Connecting to MongoDB Atlas...")
            self.atlas_client = MongoClient(self.atlas_uri, serverSelectionTimeoutMS=10000)
            self.atlas_db = self.atlas_client[self.atlas_db_name]
            self.atlas_client.admin.command('ping')
            print("✅ MongoDB Atlas connected")
            return True
            
        except Exception as e:
            print(f"❌ Database connection failed: {e}")
            return False

    def inspect_collection_structure(self, collection_name, sample_size=5):
        """Inspect the structure of a collection."""
        print(f"\n🔍 INSPECTING COLLECTION: {collection_name}")
        print("=" * 60)
        
        try:
            collection = self.atlas_db[collection_name]
            total_count = collection.count_documents({})
            
            print(f"📊 Total documents: {total_count:,}")
            
            if total_count == 0:
                print("⚠️  Collection is empty")
                return
            
            # Get sample documents
            sample_docs = list(collection.find({}).limit(sample_size))
            
            print(f"\n📋 SAMPLE DOCUMENTS (showing {len(sample_docs)} of {total_count}):")
            print("-" * 40)
            
            for i, doc in enumerate(sample_docs, 1):
                print(f"\nDocument {i}:")
                # Pretty print the document structure
                self._print_document_structure(doc, indent=2)
            
            # Analyze field frequency
            print(f"\n📈 FIELD ANALYSIS (based on {min(100, total_count)} documents):")
            print("-" * 40)
            
            field_counts = defaultdict(int)
            field_types = defaultdict(set)
            
            analysis_docs = list(collection.find({}).limit(100))
            
            for doc in analysis_docs:
                self._analyze_document_fields(doc, field_counts, field_types)
            
            # Sort fields by frequency
            sorted_fields = sorted(field_counts.items(), key=lambda x: x[1], reverse=True)
            
            for field, count in sorted_fields:
                percentage = (count / len(analysis_docs)) * 100
                types = ', '.join(sorted(field_types[field]))
                print(f"  {field}: {count}/{len(analysis_docs)} ({percentage:.1f}%) - Types: {types}")
            
        except Exception as e:
            print(f"❌ Error inspecting collection {collection_name}: {e}")

    def _print_document_structure(self, doc, indent=0):
        """Print document structure in a readable format."""
        indent_str = " " * indent
        
        for key, value in doc.items():
            if isinstance(value, dict):
                print(f"{indent_str}{key}: {{")
                self._print_document_structure(value, indent + 2)
                print(f"{indent_str}}}")
            elif isinstance(value, list):
                if value and isinstance(value[0], dict):
                    print(f"{indent_str}{key}: [")
                    if len(value) > 0:
                        print(f"{indent_str}  # Array of {len(value)} objects, first item:")
                        self._print_document_structure(value[0], indent + 4)
                    print(f"{indent_str}]")
                else:
                    print(f"{indent_str}{key}: [{type(value[0]).__name__ if value else 'empty'}] (length: {len(value)})")
            else:
                value_str = str(value)
                if len(value_str) > 50:
                    value_str = value_str[:47] + "..."
                print(f"{indent_str}{key}: {value_str} ({type(value).__name__})")

    def _analyze_document_fields(self, doc, field_counts, field_types, prefix=""):
        """Recursively analyze document fields."""
        for key, value in doc.items():
            full_key = f"{prefix}.{key}" if prefix else key
            field_counts[full_key] += 1
            field_types[full_key].add(type(value).__name__)
            
            if isinstance(value, dict):
                self._analyze_document_fields(value, field_counts, field_types, full_key)
            elif isinstance(value, list) and value and isinstance(value[0], dict):
                # Analyze first item in array of objects
                self._analyze_document_fields(value[0], field_counts, field_types, f"{full_key}[]")

    def analyze_review_system(self):
        """Analyze the review system structure."""
        print("\n🔍 ANALYZING REVIEW SYSTEM STRUCTURE")
        print("=" * 60)
        
        # Check for review-related collections
        collections = self.atlas_db.list_collection_names()
        review_collections = [c for c in collections if 'review' in c.lower() or 'rating' in c.lower()]
        
        print(f"📦 Review-related collections found: {review_collections}")
        
        for collection_name in review_collections:
            self.inspect_collection_structure(collection_name, sample_size=3)
        
        # Also check recipe_likes and recipe_comments as they might contain rating info
        other_relevant = ['recipe_likes', 'recipe_comments', 'recipe_verifications']
        for collection_name in other_relevant:
            if collection_name in collections:
                self.inspect_collection_structure(collection_name, sample_size=3)

    def analyze_user_recipe_interactions(self):
        """Analyze how users interact with recipes."""
        print("\n🔍 ANALYZING USER-RECIPE INTERACTIONS")
        print("=" * 60)
        
        try:
            # Check users collection for saved recipes or review history
            if 'users' in self.atlas_db.list_collection_names():
                print("\n👤 USER COLLECTION ANALYSIS:")
                user_sample = list(self.atlas_db.users.find({}).limit(3))
                
                for i, user in enumerate(user_sample, 1):
                    print(f"\nUser {i} structure:")
                    self._print_document_structure(user, indent=2)
            
            # Check if recipes have embedded ratings/reviews
            if 'recipes' in self.atlas_db.list_collection_names():
                print("\n🍳 RECIPE COLLECTION ANALYSIS:")
                recipe_sample = list(self.atlas_db.recipes.find({}).limit(2))
                
                for i, recipe in enumerate(recipe_sample, 1):
                    print(f"\nRecipe {i} structure:")
                    self._print_document_structure(recipe, indent=2)
                    
        except Exception as e:
            print(f"❌ Error analyzing user-recipe interactions: {e}")

    def find_missing_review_data(self):
        """Try to understand why recipe_reviews collection is missing."""
        print("\n🔍 INVESTIGATING MISSING REVIEW DATA")
        print("=" * 60)
        
        try:
            # Check if review_votes references reviews that don't exist
            if 'review_votes' in self.atlas_db.list_collection_names():
                print("\n📊 REVIEW_VOTES ANALYSIS:")
                
                # Get sample review votes
                vote_sample = list(self.atlas_db.review_votes.find({}).limit(10))
                
                if vote_sample:
                    print(f"Sample review votes:")
                    for i, vote in enumerate(vote_sample[:3], 1):
                        print(f"\nVote {i}:")
                        self._print_document_structure(vote, indent=2)
                    
                    # Check if review_ids in votes point to existing reviews
                    review_ids = [vote.get('review_id') for vote in vote_sample if vote.get('review_id')]
                    
                    if review_ids:
                        print(f"\n🔍 Checking if review IDs exist in recipe_reviews collection...")
                        
                        # Try to find these reviews
                        if 'recipe_reviews' in self.atlas_db.list_collection_names():
                            found_reviews = list(self.atlas_db.recipe_reviews.find({
                                '_id': {'$in': [ObjectId(rid) if ObjectId.is_valid(str(rid)) else rid for rid in review_ids[:5]]}
                            }))
                            print(f"Found {len(found_reviews)} reviews out of {len(review_ids[:5])} checked")
                        else:
                            print("❌ recipe_reviews collection does not exist!")
                            print("💡 This suggests reviews were deleted or stored differently")
                
                # Analyze vote patterns
                total_votes = self.atlas_db.review_votes.count_documents({})
                unique_review_ids = len(self.atlas_db.review_votes.distinct('review_id'))
                unique_user_ids = len(self.atlas_db.review_votes.distinct('user_id'))
                
                print(f"\n📊 VOTE STATISTICS:")
                print(f"   Total votes: {total_votes:,}")
                print(f"   Unique reviews voted on: {unique_review_ids:,}")
                print(f"   Unique users who voted: {unique_user_ids:,}")
                
                if unique_review_ids > 0:
                    avg_votes_per_review = total_votes / unique_review_ids
                    print(f"   Average votes per review: {avg_votes_per_review:.2f}")
                
        except Exception as e:
            print(f"❌ Error investigating missing review data: {e}")

    def generate_recommendations(self):
        """Generate recommendations based on findings."""
        print("\n💡 RECOMMENDATIONS FOR DATA OPTIMIZATION")
        print("=" * 60)
        
        collections = self.atlas_db.list_collection_names()
        
        recommendations = []
        
        # Check if recipe_reviews is missing but review_votes exists
        if 'review_votes' in collections and 'recipe_reviews' not in collections:
            recommendations.append({
                'priority': 'HIGH',
                'issue': 'Missing recipe_reviews collection',
                'description': 'You have 10,739 review votes but no recipe_reviews collection',
                'action': 'Investigate if reviews were accidentally deleted or stored elsewhere',
                'impact': 'Cannot provide recipe recommendations without review data'
            })
        
        # Check for data imbalance
        if 'recipes' in collections and 'users' in collections:
            recipe_count = self.atlas_db.recipes.count_documents({})
            user_count = self.atlas_db.users.count_documents({})
            
            if user_count > 0 and recipe_count > 0:
                if 'review_votes' in collections:
                    vote_count = self.atlas_db.review_votes.count_documents({})
                    if vote_count > recipe_count * 10:
                        recommendations.append({
                            'priority': 'MEDIUM',
                            'issue': 'High vote-to-recipe ratio',
                            'description': f'{vote_count:,} votes for {recipe_count:,} recipes',
                            'action': 'Consider if some votes are from test/fake data',
                            'impact': 'May skew recommendation algorithms'
                        })
        
        # Print recommendations
        if recommendations:
            for i, rec in enumerate(recommendations, 1):
                print(f"\n{i}. [{rec['priority']}] {rec['issue']}")
                print(f"   Description: {rec['description']}")
                print(f"   Action: {rec['action']}")
                print(f"   Impact: {rec['impact']}")
        else:
            print("\n✅ No critical issues detected in database structure")

    def cleanup_connections(self):
        """Clean up database connections."""
        try:
            if self.atlas_client:
                self.atlas_client.close()
        except Exception as e:
            print(f"⚠️  Error closing connections: {e}")

    def run_inspection(self):
        """Run the complete database inspection."""
        print("🔍 SISARASA DATABASE STRUCTURE INSPECTION")
        print("=" * 60)
        print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        try:
            if not self.load_atlas_config():
                return False
            
            if not self.connect_database():
                return False
            
            # Analyze review system
            self.analyze_review_system()
            
            # Analyze user-recipe interactions
            self.analyze_user_recipe_interactions()
            
            # Investigate missing review data
            self.find_missing_review_data()
            
            # Generate recommendations
            self.generate_recommendations()
            
            print(f"\n⏰ Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            return True
            
        except Exception as e:
            print(f"❌ Inspection failed: {e}")
            return False
        finally:
            self.cleanup_connections()


def main():
    """Main function to run the inspection."""
    print("SisaRasa Database Structure Inspector")
    print("-" * 40)
    
    inspector = DatabaseInspector()
    success = inspector.run_inspection()
    
    if success:
        print("\n✅ Database inspection completed!")
        print("Review the analysis above to understand your data structure.")
    else:
        print("\n❌ Database inspection failed!")


if __name__ == "__main__":
    main()
