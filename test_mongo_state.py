#!/usr/bin/env python3
"""
Test the current state of MongoDB connection
"""

import requests
import sys
import os

# Add the src directory to the path
src_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src')
sys.path.insert(0, src_dir)
sys.path.insert(0, os.path.join(src_dir, 'api'))

def test_mongo_state():
    """Test the current state of MongoDB variables"""
    print("🔍 Testing MongoDB State...")
    print("=" * 50)
    
    try:
        from api.models.user import mongo_client, mongo_db, mongo
        
        print(f"📊 mongo_client: {mongo_client}")
        print(f"📊 mongo_client type: {type(mongo_client)}")
        
        print(f"📊 mongo_db: {mongo_db}")
        print(f"📊 mongo_db type: {type(mongo_db)}")
        
        print(f"📊 mongo: {mongo}")
        print(f"📊 mongo type: {type(mongo)}")
        print(f"📊 mongo.db: {mongo.db}")
        print(f"📊 mongo.db type: {type(mongo.db)}")
        
        # Test if we can access the database
        if mongo_db is not None:
            try:
                result = mongo_db.command('ping')
                print(f"✅ Direct mongo_db ping: {result}")
            except Exception as e:
                print(f"❌ Direct mongo_db ping failed: {e}")
        
        if mongo.db is not None:
            try:
                result = mongo.db.command('ping')
                print(f"✅ Mock mongo.db ping: {result}")
            except Exception as e:
                print(f"❌ Mock mongo.db ping failed: {e}")
        
        # Test user lookup
        from api.models.user import get_user_by_email
        print("\n🔍 Testing get_user_by_email...")
        user = get_user_by_email("<EMAIL>")
        print(f"📊 User result: {user}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing MongoDB state: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_server_health():
    """Test server health endpoint"""
    print("\n🔍 Testing Server Health...")
    print("=" * 50)
    
    try:
        response = requests.get("http://localhost:5000/health", timeout=5)
        print(f"📊 Health Status Code: {response.status_code}")
        print(f"📊 Health Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False

def main():
    """Main test function"""
    print("🐛 MONGODB STATE DEBUG")
    print("=" * 70)
    
    # Test 1: MongoDB state
    mongo_state_success = test_mongo_state()
    
    # Test 2: Server health
    server_health_success = test_server_health()
    
    print("\n" + "=" * 70)
    print("📊 DEBUG RESULTS")
    print("=" * 70)
    print(f"🗄️  MongoDB State: {'✅ PASS' if mongo_state_success else '❌ FAIL'}")
    print(f"🌐 Server Health: {'✅ PASS' if server_health_success else '❌ FAIL'}")

if __name__ == "__main__":
    main()
