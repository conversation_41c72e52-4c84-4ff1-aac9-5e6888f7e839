#!/usr/bin/env python3
"""
Test script to verify saved recipes functionality with ingredient decoding
"""

import sys
import os
sys.path.append('src')

from api.models.user import mongo, get_user_by_id
from api.models.recipe import get_saved_recipes_for_user, decode_ingredient_id, decode_recipe_ingredients
from api.app import create_app

def test_ingredient_decoding():
    """Test the ingredient decoding functionality"""
    print("=" * 60)
    print("🧪 TESTING INGREDIENT DECODING")
    print("=" * 60)

    # Test individual ingredient decoding
    test_ingredients = [
        'grain_2775',
        'fruit_4987',
        'vegetable_1297',
        'protein_367',
        'dairy_3184',
        'condiment_6335',
        'chicken',  # Already human-readable
        'unknown_ingredient_123'
    ]

    print("Testing individual ingredient decoding:")
    for ingredient in test_ingredients:
        decoded = decode_ingredient_id(ingredient)
        print(f"  '{ingredient}' → '{decoded}'")

    # Test list decoding
    print(f"\nTesting list decoding:")
    decoded_list = decode_recipe_ingredients(test_ingredients)
    print(f"  Original: {test_ingredients[:3]}...")
    print(f"  Decoded:  {decoded_list[:3]}...")

def test_saved_recipes_with_fix():
    """Test the saved recipes functionality with the new decoding fix"""

    # Create app to get context
    app = create_app()

    with app.app_context():
        print("=" * 60)
        print("🔍 TESTING FIXED SAVED RECIPES FUNCTIONALITY")
        print("=" * 60)

        try:
            # Test database connection
            result = mongo.db.command('ping')
            print("✅ Database connection successful")

            # Find users with saved recipes
            users_with_saved = list(mongo.db.users.find(
                {"saved_recipes": {"$exists": True, "$ne": []}},
                {"email": 1, "saved_recipes": 1}
            ))

            print(f"\n🔍 Users with saved recipes: {len(users_with_saved)}")

            if users_with_saved:
                # Test with first user
                user = users_with_saved[0]
                user_id = str(user['_id'])
                email = user.get('email', 'No email')
                saved_recipe_ids = user.get('saved_recipes', [])

                print(f"\n👤 Testing with user: {email}")
                print(f"   User ID: {user_id}")
                print(f"   Number of saved recipes: {len(saved_recipe_ids)}")

                # Test the FIXED get_saved_recipes_for_user function
                print(f"\n🔧 Testing FIXED get_saved_recipes_for_user function...")
                saved_recipes = get_saved_recipes_for_user(user_id)
                print(f"✅ Retrieved {len(saved_recipes)} recipes with decoded ingredients")

                # Show details of first recipe
                if saved_recipes:
                    recipe = saved_recipes[0]
                    print(f"\n📋 Sample decoded recipe:")
                    print(f"   Name: {recipe.get('name', 'No name')}")
                    print(f"   Ingredients: {recipe.get('ingredients', [])[:5]}...")  # Show first 5
                    print(f"   Steps available: {'Yes' if recipe.get('steps') else 'No'}")
                    print(f"   Instructions available: {'Yes' if recipe.get('instructions') else 'No'}")

                    # Compare with raw database version
                    from bson.objectid import ObjectId
                    raw_recipe = mongo.db.recipes.find_one({'_id': ObjectId(saved_recipe_ids[0])})
                    if raw_recipe:
                        print(f"\n🔍 Comparison with raw database:")
                        print(f"   Raw ingredients: {raw_recipe.get('ingredients', [])[:3]}...")
                        print(f"   Decoded ingredients: {recipe.get('ingredients', [])[:3]}...")

            else:
                print("❌ No users found with saved recipes!")

        except Exception as e:
            print(f"❌ Test failed: {e}")
            import traceback
            traceback.print_exc()

def main():
    """Run all tests"""
    test_ingredient_decoding()
    print("\n")
    test_saved_recipes_with_fix()

if __name__ == "__main__":
    main()
