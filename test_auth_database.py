#!/usr/bin/env python3
"""
Test authentication database connection for SisaRasa
"""

import os
import sys
import requests
import json
from dotenv import load_dotenv

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_auth_database():
    """Test the authentication database connection."""
    print("🔍 Testing Authentication Database Connection")
    print("=" * 50)
    
    # Load environment variables
    load_dotenv()
    
    # Test configuration
    base_url = "http://localhost:5000"
    
    print("🔄 Step 1: Testing database debug endpoint...")
    
    try:
        # Test the debug endpoint
        debug_response = requests.get(
            f"{base_url}/api/auth/debug-mongo",
            timeout=10
        )
        
        print(f"📤 Debug response status: {debug_response.status_code}")
        print(f"📤 Debug response: {debug_response.text}")
        
        if debug_response.status_code == 200:
            debug_result = debug_response.json()
            print("✅ Database debug endpoint accessible")
            print(f"   Database status: {debug_result.get('database_status', 'Unknown')}")
            print(f"   User count: {debug_result.get('user_count', 'Unknown')}")
        else:
            print(f"❌ Debug endpoint failed with status {debug_response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to the server. Is the Flask app running?")
        return False
    except Exception as e:
        print(f"❌ Debug endpoint error: {e}")
        return False
    
    print("\n🔄 Step 2: Testing direct database connection...")
    
    try:
        # Test direct database connection
        from pymongo import MongoClient
        mongo_uri = os.getenv('MONGO_URI')
        client = MongoClient(mongo_uri)
        db = client['sisarasa']
        
        # Test user collection
        user_count = db.users.count_documents({})
        print(f"✅ Direct database connection successful")
        print(f"   Users in database: {user_count}")
        
        # Test creating a user directly
        test_user = {
            'name': 'Test User Direct',
            'email': '<EMAIL>',
            'password': 'hashed_password_here',
            'created_at': __import__('datetime').datetime.utcnow()
        }
        
        # Check if user exists first
        existing_user = db.users.find_one({'email': test_user['email']})
        if existing_user:
            print(f"   Test user already exists: {existing_user['_id']}")
        else:
            result = db.users.insert_one(test_user)
            print(f"   Test user created directly: {result.inserted_id}")
            
            # Clean up
            db.users.delete_one({'_id': result.inserted_id})
            print(f"   Test user cleaned up")
        
        client.close()
        
    except Exception as e:
        print(f"❌ Direct database connection error: {e}")
        return False
    
    print("\n🔄 Step 3: Testing user creation via API...")
    
    # Test user creation
    test_user_data = {
        "name": "API Test User",
        "email": "<EMAIL>",
        "password": "testpassword123"
    }
    
    try:
        signup_response = requests.post(
            f"{base_url}/api/auth/signup",
            json=test_user_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"📤 Signup response status: {signup_response.status_code}")
        print(f"📤 Signup response: {signup_response.text}")
        
        if signup_response.status_code == 201:
            signup_result = signup_response.json()
            print("✅ User creation via API successful!")
            print(f"   User ID: {signup_result.get('user_id', 'N/A')}")
            return True
        else:
            print(f"❌ User creation failed with status {signup_response.status_code}")
            try:
                error_data = signup_response.json()
                print(f"   Error: {error_data.get('message', 'Unknown error')}")
            except:
                print(f"   Raw response: {signup_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API user creation error: {e}")
        return False

if __name__ == "__main__":
    success = test_auth_database()
    if success:
        print("\n🎯 Authentication database test PASSED")
    else:
        print("\n❌ Authentication database test FAILED")
