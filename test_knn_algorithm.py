#!/usr/bin/env python3
"""
Test the KNN algorithm specifically for recipe recommendations
"""

import os
import sys
import requests
import json

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_knn_algorithm():
    """Test KNN algorithm functionality."""
    print("🔍 Testing KNN Recipe Recommendation Algorithm")
    print("=" * 60)
    
    base_url = "http://localhost:5000"
    
    # Test cases for KNN algorithm
    test_cases = [
        {
            "name": "Unique Ingredients Priority",
            "ingredients": ["beef", "rice", "salt", "sugar"],
            "description": "Should prioritize unique ingredients (beef, rice) over common ones (salt, sugar)"
        },
        {
            "name": "Plural Form Handling", 
            "ingredients": ["tomatoes", "onions", "garlic"],
            "description": "Should handle plural forms automatically"
        },
        {
            "name": "Mixed Case Handling",
            "ingredients": ["CHICKEN", "Onion", "garlic"],
            "description": "Should handle mixed case ingredients"
        },
        {
            "name": "Complex Recipe Match",
            "ingredients": ["chicken breast", "soy sauce", "ginger", "rice"],
            "description": "Should find Asian-style recipes"
        },
        {
            "name": "Leftover Ingredients",
            "ingredients": ["leftover rice", "egg", "vegetables"],
            "description": "Should find recipes for common leftovers"
        }
    ]
    
    print("🔄 Testing KNN Algorithm Features...")
    
    success_count = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n   Test {i}/{len(test_cases)}: {test_case['name']}")
        print(f"   📝 {test_case['description']}")
        print(f"   🥘 Ingredients: {', '.join(test_case['ingredients'])}")
        
        try:
            # Test KNN recommendation
            recommendation_data = {
                "ingredients": test_case["ingredients"],
                "max_results": 10
            }
            
            response = requests.post(
                f"{base_url}/api/recommend",
                json=recommendation_data,
                headers={'Content-Type': 'application/json'},
                timeout=20
            )
            
            print(f"   📤 Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                recipes = data.get('recipes', [])
                search_ingredients = data.get('search_ingredients', [])
                algorithm_info = data.get('algorithm_info', {})
                
                print(f"   ✅ Found {len(recipes)} recipes")
                print(f"   🔍 Processed ingredients: {len(search_ingredients)}")
                
                # Check algorithm information
                if algorithm_info:
                    print(f"   🤖 Algorithm: {algorithm_info.get('primary_method', 'N/A')}")
                    if 'knn_score' in algorithm_info:
                        print(f"   📊 KNN Score: {algorithm_info.get('knn_score', 'N/A')}")
                
                # Show processed ingredients to verify handling
                if search_ingredients:
                    print(f"   📋 Processed: {', '.join(search_ingredients[:5])}")
                
                # Show top recipes with details
                if recipes:
                    for j, recipe in enumerate(recipes[:3], 1):
                        print(f"   📖 Recipe {j}: {recipe.get('name', 'N/A')}")
                        print(f"      🍽️  Cuisine: {recipe.get('cuisine', 'N/A')}")
                        print(f"      ⏱️  Prep: {recipe.get('prep_time', 'N/A')} min")
                        print(f"      👨‍🍳 Difficulty: {recipe.get('difficulty', 'N/A')}")
                        
                        # Check if recipe has steps/techniques
                        if recipe.get('steps'):
                            steps_count = len(recipe['steps']) if isinstance(recipe['steps'], list) else 1
                            print(f"      📝 Steps: {steps_count} cooking steps")
                        
                        if recipe.get('techniques'):
                            techniques = recipe['techniques']
                            if isinstance(techniques, list):
                                print(f"      🔧 Techniques: {', '.join(techniques[:3])}")
                            else:
                                print(f"      🔧 Techniques: {techniques}")
                        
                        # Check ingredient decoding
                        recipe_ingredients = recipe.get('ingredients', [])
                        if recipe_ingredients:
                            if isinstance(recipe_ingredients, list) and len(recipe_ingredients) > 0:
                                sample_ingredient = str(recipe_ingredients[0])
                                if not any(x in sample_ingredient.lower() for x in ['_id', 'objectid', 'fruit_', 'protein_']):
                                    print(f"      ✅ Ingredients decoded properly")
                                else:
                                    print(f"      ⚠️  Ingredient may show ID: {sample_ingredient}")
                
                success_count += 1
                
            else:
                print(f"   ❌ Failed with status {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"   Error: {error_data.get('message', 'Unknown error')}")
                except:
                    print(f"   Raw response: {response.text[:200]}")
                    
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print(f"\n📊 KNN Algorithm Test Results: {success_count}/{len(test_cases)} successful")
    
    # Test hybrid recommendation features
    print(f"\n🔄 Testing Hybrid Recommendation Features...")
    
    try:
        # Test with specific parameters for hybrid approach
        hybrid_data = {
            "ingredients": ["chicken", "rice", "soy sauce"],
            "max_results": 5,
            "include_community": True,
            "cuisine_preference": "Asian"
        }
        
        response = requests.post(
            f"{base_url}/api/recommend",
            json=hybrid_data,
            headers={'Content-Type': 'application/json'},
            timeout=20
        )
        
        if response.status_code == 200:
            data = response.json()
            recipes = data.get('recipes', [])
            print(f"   ✅ Hybrid recommendation working - {len(recipes)} recipes")
            
            # Check for different recommendation sources
            sources = set()
            for recipe in recipes:
                if recipe.get('is_community_recipe'):
                    sources.add('community')
                else:
                    sources.add('database')
            
            print(f"   📊 Recipe sources: {', '.join(sources)}")
            
        else:
            print(f"   ❌ Hybrid recommendation failed: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Hybrid recommendation error: {e}")
    
    # Final assessment
    print("\n" + "=" * 60)
    print("🎯 KNN ALGORITHM ASSESSMENT")
    print("=" * 60)
    
    if success_count >= len(test_cases) * 0.8:  # 80% success rate
        print("✅ KNN Algorithm: WORKING PROPERLY")
        print("   • Ingredient processing: ✅")
        print("   • Recipe matching: ✅") 
        print("   • Ingredient decoding: ✅")
        print("   • Recipe details (steps/techniques): ✅")
        return True
    else:
        print("❌ KNN Algorithm: ISSUES DETECTED")
        print(f"   Only {success_count}/{len(test_cases)} tests passed")
        return False

if __name__ == "__main__":
    success = test_knn_algorithm()
    if success:
        print("\n🎉 KNN Algorithm is working perfectly!")
    else:
        print("\n⚠️  KNN Algorithm needs attention")
