#!/usr/bin/env python3
"""
SisaRasa Data Distribution Analysis
==================================

This script analyzes the current data distribution in your SisaRasa database
to identify imbalances and optimization opportunities for recommendation quality.
"""

import os
import sys
from datetime import datetime, timed<PERSON>ta
from pymongo import MongoClient
from bson import ObjectId
import json
from collections import defaultdict, Counter
import statistics
import re

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

class DataAnalyzer:
    def __init__(self):
        """Initialize the data analyzer."""
        self.atlas_uri = None
        self.atlas_db_name = "sisarasa"
        self.atlas_client = None
        self.atlas_db = None
        
        self.analysis_results = {}

    def load_atlas_config(self):
        """Load MongoDB Atlas connection string."""
        try:
            env_path = '.env'
            if os.path.exists(env_path):
                with open(env_path, 'r') as f:
                    for line in f:
                        if line.startswith('MONGO_URI='):
                            self.atlas_uri = line.split('=', 1)[1].strip().strip('"\'')
                            break
            
            if not self.atlas_uri:
                self.atlas_uri = os.getenv('MONGO_URI')
            
            if not self.atlas_uri:
                raise ValueError("MongoDB Atlas URI not found")
                
            return True
            
        except Exception as e:
            print(f"❌ Error loading Atlas configuration: {e}")
            return False

    def connect_database(self):
        """Connect to Atlas database."""
        try:
            print("🔗 Connecting to MongoDB Atlas...")
            self.atlas_client = MongoClient(self.atlas_uri, serverSelectionTimeoutMS=10000)
            self.atlas_db = self.atlas_client[self.atlas_db_name]
            self.atlas_client.admin.command('ping')
            print("✅ MongoDB Atlas connected")
            return True
            
        except Exception as e:
            print(f"❌ Database connection failed: {e}")
            return False

    def analyze_collection_overview(self):
        """Analyze basic collection statistics."""
        print("\n📊 COLLECTION OVERVIEW")
        print("=" * 50)
        
        collections = self.atlas_db.list_collection_names()
        overview = {}
        
        for collection_name in sorted(collections):
            if not collection_name.startswith('system.'):
                count = self.atlas_db[collection_name].count_documents({})
                overview[collection_name] = count
                print(f"📦 {collection_name}: {count:,} documents")
        
        self.analysis_results['collection_overview'] = overview
        return overview

    def analyze_recipe_review_ratio(self):
        """Analyze the recipe-to-review ratio."""
        print("\n🍳 RECIPE-REVIEW RATIO ANALYSIS")
        print("=" * 50)
        
        try:
            # Count recipes
            recipe_count = 0
            recipe_sources = {}
            
            # Check different recipe sources
            if 'recipes' in self.atlas_db.list_collection_names():
                recipes_count = self.atlas_db.recipes.count_documents({})
                recipe_sources['recipes'] = recipes_count
                recipe_count += recipes_count
            
            if 'community_recipes' in self.atlas_db.list_collection_names():
                community_count = self.atlas_db.community_recipes.count_documents({})
                recipe_sources['community_recipes'] = community_count
                recipe_count += community_count
            
            # Count reviews
            review_count = 0
            if 'recipe_reviews' in self.atlas_db.list_collection_names():
                review_count = self.atlas_db.recipe_reviews.count_documents({})
            
            print(f"📊 Recipe Sources:")
            for source, count in recipe_sources.items():
                print(f"   • {source}: {count:,}")
            print(f"📊 Total Recipes: {recipe_count:,}")
            print(f"📊 Total Reviews: {review_count:,}")
            
            if recipe_count > 0:
                ratio = review_count / recipe_count
                print(f"📊 Review-to-Recipe Ratio: {ratio:.2f}")
                
                if ratio > 10:
                    print("⚠️  HIGH RATIO: Too many reviews per recipe (may indicate fake data)")
                elif ratio < 1:
                    print("⚠️  LOW RATIO: Not enough reviews for effective recommendations")
                else:
                    print("✅ GOOD RATIO: Balanced review distribution")
            
            self.analysis_results['recipe_review_ratio'] = {
                'recipe_count': recipe_count,
                'review_count': review_count,
                'ratio': ratio if recipe_count > 0 else 0,
                'recipe_sources': recipe_sources
            }
            
        except Exception as e:
            print(f"❌ Error analyzing recipe-review ratio: {e}")

    def analyze_review_distribution(self):
        """Analyze review distribution across recipes."""
        print("\n📈 REVIEW DISTRIBUTION ANALYSIS")
        print("=" * 50)
        
        try:
            if 'recipe_reviews' not in self.atlas_db.list_collection_names():
                print("⚠️  No recipe_reviews collection found")
                return
            
            # Get review distribution per recipe
            pipeline = [
                {'$group': {
                    '_id': '$recipe_id',
                    'review_count': {'$sum': 1},
                    'avg_rating': {'$avg': '$rating'},
                    'ratings': {'$push': '$rating'}
                }},
                {'$sort': {'review_count': -1}}
            ]
            
            review_distribution = list(self.atlas_db.recipe_reviews.aggregate(pipeline))
            
            if not review_distribution:
                print("⚠️  No reviews found")
                return
            
            # Analyze distribution
            review_counts = [item['review_count'] for item in review_distribution]
            avg_ratings = [item['avg_rating'] for item in review_distribution if item['avg_rating']]
            
            print(f"📊 Recipes with reviews: {len(review_distribution):,}")
            print(f"📊 Average reviews per recipe: {statistics.mean(review_counts):.2f}")
            print(f"📊 Median reviews per recipe: {statistics.median(review_counts):.2f}")
            print(f"📊 Max reviews on single recipe: {max(review_counts):,}")
            print(f"📊 Min reviews on single recipe: {min(review_counts):,}")
            
            # Show recipes with excessive reviews (potential fake data)
            excessive_threshold = statistics.mean(review_counts) + 2 * statistics.stdev(review_counts)
            excessive_reviews = [item for item in review_distribution if item['review_count'] > excessive_threshold]
            
            if excessive_reviews:
                print(f"\n⚠️  RECIPES WITH EXCESSIVE REVIEWS (>{excessive_threshold:.0f}):")
                for item in excessive_reviews[:10]:  # Show top 10
                    print(f"   Recipe {item['_id']}: {item['review_count']} reviews, avg rating: {item['avg_rating']:.2f}")
            
            # Show recipes with no reviews (cold start problem)
            recipes_without_reviews = self.find_recipes_without_reviews()
            print(f"\n❄️  Recipes without reviews: {recipes_without_reviews:,}")
            
            # Rating distribution
            if avg_ratings:
                print(f"\n⭐ RATING ANALYSIS:")
                print(f"   Average rating across all recipes: {statistics.mean(avg_ratings):.2f}")
                print(f"   Rating standard deviation: {statistics.stdev(avg_ratings):.2f}")
            
            self.analysis_results['review_distribution'] = {
                'recipes_with_reviews': len(review_distribution),
                'avg_reviews_per_recipe': statistics.mean(review_counts),
                'median_reviews_per_recipe': statistics.median(review_counts),
                'excessive_reviews_threshold': excessive_threshold,
                'excessive_review_recipes': len(excessive_reviews),
                'recipes_without_reviews': recipes_without_reviews,
                'avg_rating_overall': statistics.mean(avg_ratings) if avg_ratings else 0
            }
            
        except Exception as e:
            print(f"❌ Error analyzing review distribution: {e}")

    def find_recipes_without_reviews(self):
        """Find recipes that have no reviews."""
        try:
            # Get all recipe IDs that have reviews
            reviewed_recipe_ids = set()
            if 'recipe_reviews' in self.atlas_db.list_collection_names():
                reviews = self.atlas_db.recipe_reviews.find({}, {'recipe_id': 1})
                reviewed_recipe_ids = {review['recipe_id'] for review in reviews}
            
            # Count total recipes
            total_recipes = 0
            if 'recipes' in self.atlas_db.list_collection_names():
                total_recipes += self.atlas_db.recipes.count_documents({})
            if 'community_recipes' in self.atlas_db.list_collection_names():
                total_recipes += self.atlas_db.community_recipes.count_documents({})
            
            # Recipes without reviews = total - reviewed
            return total_recipes - len(reviewed_recipe_ids)
            
        except Exception as e:
            print(f"❌ Error finding recipes without reviews: {e}")
            return 0

    def analyze_user_patterns(self):
        """Analyze user review patterns to identify fake/test accounts."""
        print("\n👤 USER PATTERN ANALYSIS")
        print("=" * 50)
        
        try:
            if 'recipe_reviews' not in self.atlas_db.list_collection_names():
                print("⚠️  No recipe_reviews collection found")
                return
            
            # Analyze user review patterns
            pipeline = [
                {'$group': {
                    '_id': '$user_id',
                    'review_count': {'$sum': 1},
                    'avg_rating': {'$avg': '$rating'},
                    'rating_variance': {'$stdDevPop': '$rating'},
                    'reviews': {'$push': {
                        'rating': '$rating',
                        'text': '$review_text',
                        'created_at': '$created_at'
                    }}
                }},
                {'$sort': {'review_count': -1}}
            ]
            
            user_patterns = list(self.atlas_db.recipe_reviews.aggregate(pipeline))
            
            if not user_patterns:
                print("⚠️  No user review patterns found")
                return
            
            review_counts = [user['review_count'] for user in user_patterns]
            
            print(f"📊 Users who have reviewed: {len(user_patterns):,}")
            print(f"📊 Average reviews per user: {statistics.mean(review_counts):.2f}")
            print(f"📊 Median reviews per user: {statistics.median(review_counts):.2f}")
            
            # Identify suspicious users
            suspicious_users = []
            
            for user in user_patterns:
                suspicion_score = 0
                reasons = []
                
                # Too many reviews
                if user['review_count'] > 50:
                    suspicion_score += 2
                    reasons.append(f"High review count ({user['review_count']})")
                
                # All same rating
                if user['rating_variance'] == 0 and user['review_count'] > 5:
                    suspicion_score += 3
                    reasons.append("All identical ratings")
                
                # Check for repetitive text patterns
                review_texts = [r.get('text', '') for r in user['reviews'] if r.get('text')]
                if len(review_texts) > 3:
                    unique_texts = len(set(review_texts))
                    if unique_texts / len(review_texts) < 0.5:
                        suspicion_score += 2
                        reasons.append("Repetitive review text")
                
                if suspicion_score >= 3:
                    suspicious_users.append({
                        'user_id': user['_id'],
                        'review_count': user['review_count'],
                        'avg_rating': user['avg_rating'],
                        'suspicion_score': suspicion_score,
                        'reasons': reasons
                    })
            
            print(f"\n🚨 SUSPICIOUS USERS DETECTED: {len(suspicious_users)}")
            for user in suspicious_users[:10]:  # Show top 10
                print(f"   User {user['user_id']}: {user['review_count']} reviews, "
                      f"avg {user['avg_rating']:.1f}★ - {', '.join(user['reasons'])}")
            
            self.analysis_results['user_patterns'] = {
                'total_reviewing_users': len(user_patterns),
                'avg_reviews_per_user': statistics.mean(review_counts),
                'suspicious_users': len(suspicious_users),
                'suspicious_user_details': suspicious_users[:20]  # Store top 20
            }
            
        except Exception as e:
            print(f"❌ Error analyzing user patterns: {e}")

    def analyze_review_quality(self):
        """Analyze review text quality and authenticity."""
        print("\n📝 REVIEW QUALITY ANALYSIS")
        print("=" * 50)
        
        try:
            if 'recipe_reviews' not in self.atlas_db.list_collection_names():
                print("⚠️  No recipe_reviews collection found")
                return
            
            # Sample reviews for analysis
            reviews = list(self.atlas_db.recipe_reviews.find({}).limit(1000))
            
            if not reviews:
                print("⚠️  No reviews found")
                return
            
            # Analyze review characteristics
            text_reviews = [r for r in reviews if r.get('review_text')]
            empty_reviews = len(reviews) - len(text_reviews)
            
            print(f"📊 Total reviews analyzed: {len(reviews):,}")
            print(f"📊 Reviews with text: {len(text_reviews):,}")
            print(f"📊 Reviews without text: {empty_reviews:,}")
            
            if text_reviews:
                # Text length analysis
                text_lengths = [len(r['review_text']) for r in text_reviews]
                avg_length = statistics.mean(text_lengths)
                
                print(f"📊 Average review length: {avg_length:.1f} characters")
                print(f"📊 Median review length: {statistics.median(text_lengths):.1f} characters")
                
                # Identify very short or very long reviews
                very_short = sum(1 for length in text_lengths if length < 10)
                very_long = sum(1 for length in text_lengths if length > 500)
                
                print(f"📊 Very short reviews (<10 chars): {very_short}")
                print(f"📊 Very long reviews (>500 chars): {very_long}")
                
                # Look for template/fake patterns
                common_phrases = Counter()
                for review in text_reviews[:200]:  # Sample first 200
                    text = review['review_text'].lower()
                    # Look for common fake review patterns
                    if any(phrase in text for phrase in ['great recipe', 'loved it', 'amazing', 'perfect']):
                        common_phrases['generic_positive'] += 1
                    if any(phrase in text for phrase in ['test', 'testing', 'sample']):
                        common_phrases['test_content'] += 1
                
                print(f"\n🔍 CONTENT PATTERNS:")
                for pattern, count in common_phrases.most_common(5):
                    print(f"   {pattern}: {count} occurrences")
            
            # Rating distribution
            ratings = [r['rating'] for r in reviews]
            rating_dist = Counter(ratings)
            
            print(f"\n⭐ RATING DISTRIBUTION:")
            for rating in sorted(rating_dist.keys()):
                count = rating_dist[rating]
                percentage = (count / len(reviews)) * 100
                print(f"   {rating}★: {count:,} ({percentage:.1f}%)")
            
            # Check for unnatural rating patterns
            if rating_dist[5] > len(reviews) * 0.7:
                print("⚠️  WARNING: Too many 5-star ratings (may indicate fake reviews)")
            elif rating_dist[1] > len(reviews) * 0.3:
                print("⚠️  WARNING: Too many 1-star ratings (may indicate spam)")
            
            self.analysis_results['review_quality'] = {
                'total_reviews': len(reviews),
                'reviews_with_text': len(text_reviews),
                'avg_text_length': avg_length if text_reviews else 0,
                'rating_distribution': dict(rating_dist),
                'quality_flags': {
                    'too_many_5_stars': rating_dist[5] > len(reviews) * 0.7,
                    'too_many_1_stars': rating_dist[1] > len(reviews) * 0.3,
                    'very_short_reviews': very_short if text_reviews else 0,
                    'very_long_reviews': very_long if text_reviews else 0
                }
            }
            
        except Exception as e:
            print(f"❌ Error analyzing review quality: {e}")

    def generate_optimization_recommendations(self):
        """Generate recommendations for data optimization."""
        print("\n💡 OPTIMIZATION RECOMMENDATIONS")
        print("=" * 50)
        
        recommendations = []
        
        # Based on recipe-review ratio
        ratio_data = self.analysis_results.get('recipe_review_ratio', {})
        if ratio_data.get('ratio', 0) > 10:
            recommendations.append({
                'priority': 'HIGH',
                'category': 'Data Balance',
                'issue': 'Too many reviews per recipe',
                'action': 'Remove excessive/fake reviews, especially from suspicious users',
                'impact': 'Improve recommendation quality and reduce noise'
            })
        
        # Based on user patterns
        user_data = self.analysis_results.get('user_patterns', {})
        if user_data.get('suspicious_users', 0) > 0:
            recommendations.append({
                'priority': 'HIGH',
                'category': 'Data Quality',
                'issue': f"{user_data['suspicious_users']} suspicious users detected",
                'action': 'Remove reviews from users with repetitive patterns or excessive activity',
                'impact': 'Eliminate fake reviews and improve authenticity'
            })
        
        # Based on review distribution
        dist_data = self.analysis_results.get('review_distribution', {})
        if dist_data.get('recipes_without_reviews', 0) > 0:
            recommendations.append({
                'priority': 'MEDIUM',
                'category': 'Cold Start',
                'issue': f"{dist_data['recipes_without_reviews']} recipes without reviews",
                'action': 'Add seed reviews for popular recipes or remove unused recipes',
                'impact': 'Reduce cold start problems for new recipes'
            })
        
        # Based on review quality
        quality_data = self.analysis_results.get('review_quality', {})
        quality_flags = quality_data.get('quality_flags', {})
        
        if quality_flags.get('too_many_5_stars'):
            recommendations.append({
                'priority': 'MEDIUM',
                'category': 'Rating Balance',
                'issue': 'Unnatural rating distribution (too many 5-stars)',
                'action': 'Remove fake positive reviews and encourage honest feedback',
                'impact': 'More realistic rating distribution for better recommendations'
            })
        
        # Print recommendations
        for i, rec in enumerate(recommendations, 1):
            print(f"\n{i}. [{rec['priority']}] {rec['category']}")
            print(f"   Issue: {rec['issue']}")
            print(f"   Action: {rec['action']}")
            print(f"   Impact: {rec['impact']}")
        
        if not recommendations:
            print("✅ No major issues detected. Your data appears well-balanced!")
        
        self.analysis_results['recommendations'] = recommendations

    def save_analysis_report(self):
        """Save detailed analysis report to file."""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = f"data_analysis_report_{timestamp}.json"
        
        with open(report_file, 'w') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'analysis_results': self.analysis_results
            }, f, indent=2, default=str)
        
        print(f"\n📄 Detailed analysis report saved to: {report_file}")

    def cleanup_connections(self):
        """Clean up database connections."""
        try:
            if self.atlas_client:
                self.atlas_client.close()
        except Exception as e:
            print(f"⚠️  Error closing connections: {e}")

    def run_analysis(self):
        """Run the complete data analysis."""
        print("📊 SISARASA DATA DISTRIBUTION ANALYSIS")
        print("=" * 50)
        print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        try:
            if not self.load_atlas_config():
                return False
            
            if not self.connect_database():
                return False
            
            self.analyze_collection_overview()
            self.analyze_recipe_review_ratio()
            self.analyze_review_distribution()
            self.analyze_user_patterns()
            self.analyze_review_quality()
            self.generate_optimization_recommendations()
            self.save_analysis_report()
            
            print(f"\n⏰ Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            return True
            
        except Exception as e:
            print(f"❌ Analysis failed: {e}")
            return False
        finally:
            self.cleanup_connections()


def main():
    """Main function to run the analysis."""
    print("SisaRasa Data Distribution Analyzer")
    print("-" * 40)
    
    analyzer = DataAnalyzer()
    success = analyzer.run_analysis()
    
    if success:
        print("\n✅ Analysis completed successfully!")
        print("Review the recommendations above to optimize your data.")
    else:
        print("\n❌ Analysis failed!")


if __name__ == "__main__":
    main()
