#!/usr/bin/env python3
"""
Test MongoDB Atlas database connection for SisaRasa
"""

import os
import sys
from dotenv import load_dotenv
from pymongo import MongoClient

def test_database_connection():
    """Test the MongoDB Atlas connection."""
    print("🔍 Testing MongoDB Atlas Connection")
    print("=" * 50)
    
    # Load environment variables
    load_dotenv()
    
    # Get MongoDB URI
    mongo_uri = os.getenv('MONGO_URI')
    
    if not mongo_uri:
        print("❌ MONGO_URI environment variable not found")
        print("Please check your .env file")
        return False
    
    # Mask sensitive parts for logging
    masked_uri = mongo_uri
    if '@' in mongo_uri and '://' in mongo_uri:
        try:
            parts = mongo_uri.split('://')
            protocol = parts[0]
            rest = parts[1]
            if '@' in rest:
                credentials, host_part = rest.split('@', 1)
                masked_uri = f"{protocol}://***:***@{host_part}"
        except:
            masked_uri = "mongodb+srv://***:***@***"
    
    print(f"🔗 MongoDB URI: {masked_uri}")
    
    try:
        # Test connection
        print("🔄 Connecting to MongoDB Atlas...")
        client = MongoClient(mongo_uri, serverSelectionTimeoutMS=10000)
        
        # Test ping
        result = client.admin.command('ping')
        print(f"✅ MongoDB ping successful: {result}")
        
        # Test database access
        db = client['sisarasa']
        collections = db.list_collection_names()
        print(f"📦 Available collections: {len(collections)}")
        
        # Test specific collections needed for reviews
        required_collections = ['users', 'recipes', 'recipe_reviews']
        for collection_name in required_collections:
            if collection_name in collections:
                count = db[collection_name].count_documents({})
                print(f"   ✅ {collection_name}: {count:,} documents")
            else:
                print(f"   ⚠️  {collection_name}: collection not found")
        
        # Test a simple query
        print("\n🧪 Testing database operations...")
        
        # Test user collection
        user_count = db.users.count_documents({})
        print(f"   Users: {user_count:,}")
        
        # Test recipe collection
        recipe_count = db.recipes.count_documents({})
        print(f"   Recipes: {recipe_count:,}")
        
        # Test review collection
        review_count = db.recipe_reviews.count_documents({})
        print(f"   Reviews: {review_count:,}")
        
        client.close()
        
        print("\n✅ Database connection test PASSED")
        print("🎯 MongoDB Atlas is properly configured and accessible")
        return True
        
    except Exception as e:
        print(f"\n❌ Database connection test FAILED")
        print(f"Error: {e}")
        
        # Provide troubleshooting tips
        print("\n🔧 Troubleshooting tips:")
        print("1. Check your MongoDB Atlas cluster is running")
        print("2. Verify your IP address is whitelisted")
        print("3. Confirm your username and password are correct")
        print("4. Ensure your connection string includes the database name")
        
        return False

if __name__ == "__main__":
    test_database_connection()
