#!/usr/bin/env python3
"""
Test the saved recipes API endpoint directly
"""

import requests
import json
import sys
import os

# Add src to path
sys.path.append('src')

def test_saved_recipes_api():
    """Test the /api/recipes/saved endpoint"""
    
    base_url = "http://localhost:5000"
    
    print("=" * 60)
    print("🔍 TESTING SAVED RECIPES API ENDPOINT")
    print("=" * 60)
    
    # Test credentials (from our test)
    test_email = "<EMAIL>"
    test_password = "password123"
    
    try:
        # 1. Login to get JWT token
        print("🔐 Step 1: Logging in...")
        login_response = requests.post(f"{base_url}/api/auth/login", json={
            "email": test_email,
            "password": test_password
        })
        
        if login_response.status_code == 200:
            login_data = login_response.json()
            token = login_data.get('token')
            print(f"✅ Login successful, token received")
        else:
            print(f"❌ Login failed: {login_response.status_code} - {login_response.text}")
            return
        
        # 2. Test saved recipes endpoint
        print("\n📚 Step 2: Testing saved recipes endpoint...")
        headers = {"Authorization": f"Bearer {token}"}
        
        saved_response = requests.get(f"{base_url}/api/recipes/saved", headers=headers)
        
        if saved_response.status_code == 200:
            saved_data = saved_response.json()
            print(f"✅ Saved recipes endpoint successful")
            print(f"📊 Number of saved recipes: {len(saved_data.get('recipes', []))}")
            
            # Show details of first recipe
            recipes = saved_data.get('recipes', [])
            if recipes:
                recipe = recipes[0]
                print(f"\n📋 Sample saved recipe:")
                print(f"   Name: {recipe.get('name')}")
                print(f"   ID: {recipe.get('id')}")
                print(f"   Ingredients: {recipe.get('ingredients', [])[:5]}...")  # First 5
                print(f"   Steps: {len(recipe.get('steps', []))} steps available")
                print(f"   Techniques: {recipe.get('techniques', [])}")
                print(f"   Match %: {recipe.get('ingredient_match_percentage')}%")
                
                # Verify ingredients are human-readable (not IDs)
                ingredients = recipe.get('ingredients', [])
                has_encoded_ids = any('_' in str(ing) and any(c.isdigit() for c in str(ing)) for ing in ingredients)
                
                if has_encoded_ids:
                    print("❌ WARNING: Still contains encoded ingredient IDs!")
                else:
                    print("✅ SUCCESS: All ingredients are human-readable!")
                    
            else:
                print("ℹ️  No saved recipes found for this user")
                
        else:
            print(f"❌ Saved recipes endpoint failed: {saved_response.status_code}")
            print(f"Response: {saved_response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - make sure the server is running on localhost:5000")
        print("💡 Run: python src/run_api.py")
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_saved_recipes_api()
