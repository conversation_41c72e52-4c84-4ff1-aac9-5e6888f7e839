#!/usr/bin/env python3
"""
List all users in the database
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from pymongo import MongoClient
from dotenv import load_dotenv

def list_users():
    # Load environment variables
    load_dotenv()

    # Connect to MongoDB using environment variable
    mongo_uri = os.getenv('MONGO_URI')
    if not mongo_uri:
        print("❌ MONGO_URI not found in environment variables")
        print("Please set MONGO_URI in your .env file")
        return

    client = MongoClient(mongo_uri)
    db = client['sisarasa']
    
    # Get all users
    users = list(db.users.find({}, {'name': 1, 'email': 1, 'saved_recipes': 1}))
    
    print(f"Found {len(users)} users:")
    for user in users:
        saved_count = len(user.get('saved_recipes', []))
        print(f"  - Name: {user.get('name', 'N/A')}")
        print(f"    Email: {user.get('email', 'N/A')}")
        print(f"    ID: {user['_id']}")
        print(f"    Saved recipes: {saved_count}")
        if user.get('saved_recipes'):
            print(f"    Saved recipe IDs: {user['saved_recipes']}")
        print()

if __name__ == "__main__":
    list_users()
