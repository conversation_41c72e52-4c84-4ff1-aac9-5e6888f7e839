#!/usr/bin/env python3
"""
Debug MongoDB connection in the running Flask app
"""

import requests
import sys
import os

# Add the src directory to the path
src_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src')
sys.path.insert(0, src_dir)
sys.path.insert(0, os.path.join(src_dir, 'api'))

def test_direct_mongo_import():
    """Test importing mongo directly"""
    print("🔍 Testing Direct MongoDB Import...")
    print("=" * 50)
    
    try:
        from api.models.user import mongo
        print(f"✅ Successfully imported mongo object")
        print(f"📊 mongo object type: {type(mongo)}")
        print(f"📊 mongo object value: {mongo}")
        
        if mongo is not None:
            print(f"📊 mongo.db type: {type(mongo.db)}")
            print(f"📊 mongo.db value: {mongo.db}")
            
            if hasattr(mongo, 'cx'):
                print(f"📊 mongo.cx type: {type(mongo.cx)}")
                print(f"📊 mongo.cx value: {mongo.cx}")
        
        return mongo
    except Exception as e:
        print(f"❌ Failed to import mongo: {e}")
        return None

def test_user_functions():
    """Test user functions directly"""
    print("\n🔍 Testing User Functions...")
    print("=" * 50)
    
    try:
        from api.models.user import get_user_by_email, mongo
        print(f"✅ Successfully imported get_user_by_email function")
        
        # Test the function
        print("🔄 Testing get_user_by_<NAME_EMAIL>...")
        user = get_user_by_email("<EMAIL>")
        print(f"📊 Result: {user}")
        
        if user:
            print(f"✅ User found: {user.get('name', 'Unknown')}")
            return True
        else:
            print("❌ User not found or database error")
            return False
            
    except Exception as e:
        print(f"❌ Error testing user functions: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_flask_app_context():
    """Test if we need Flask app context"""
    print("\n🔍 Testing Flask App Context...")
    print("=" * 50)
    
    try:
        # Try to create a minimal Flask app context
        from flask import Flask
        from dotenv import load_dotenv
        
        load_dotenv()
        
        app = Flask(__name__)
        app.config['MONGO_URI'] = os.getenv('MONGO_URI')
        
        with app.app_context():
            print("✅ Created Flask app context")
            
            # Now try to initialize mongo
            from api.models.user import init_db, get_user_by_email
            
            print("🔄 Initializing database in app context...")
            init_result = init_db(app)
            print(f"📊 init_db result: {init_result}")
            
            if init_result:
                print("🔄 Testing get_user_by_email in app context...")
                user = get_user_by_email("<EMAIL>")
                print(f"📊 User result: {user}")
                
                if user:
                    print(f"✅ SUCCESS! User found: {user.get('name', 'Unknown')}")
                    return True
                else:
                    print("❌ User not found")
                    return False
            else:
                print("❌ Database initialization failed")
                return False
                
    except Exception as e:
        print(f"❌ Error with Flask app context: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main debug function"""
    print("🐛 MONGODB CONNECTION DEBUG")
    print("=" * 70)
    
    # Test 1: Direct import
    mongo_obj = test_direct_mongo_import()
    
    # Test 2: User functions
    user_func_success = test_user_functions()
    
    # Test 3: Flask app context
    app_context_success = test_flask_app_context()
    
    print("\n" + "=" * 70)
    print("📊 DEBUG RESULTS")
    print("=" * 70)
    print(f"📦 Direct Import: {'✅ PASS' if mongo_obj is not None else '❌ FAIL'}")
    print(f"👤 User Functions: {'✅ PASS' if user_func_success else '❌ FAIL'}")
    print(f"🌐 App Context: {'✅ PASS' if app_context_success else '❌ FAIL'}")
    
    if app_context_success:
        print("\n💡 SOLUTION FOUND!")
        print("The MongoDB connection works when properly initialized with Flask app context.")
        print("The issue is likely that the running Flask server has a context problem.")
    else:
        print("\n⚠️  ISSUE PERSISTS")
        print("The MongoDB connection has deeper issues that need investigation.")

if __name__ == "__main__":
    main()
