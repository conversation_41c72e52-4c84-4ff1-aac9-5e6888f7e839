#!/usr/bin/env python3
"""
Test MongoDB Atlas connection for SisaRasa
"""

import os
import sys
from pymongo import MongoClient
from dotenv import load_dotenv

def test_mongodb_connection():
    """Test MongoDB Atlas connection"""
    print("🔗 Testing MongoDB Atlas Connection...")
    print("=" * 50)
    
    # Load environment variables
    load_dotenv()
    mongo_uri = os.getenv('MONGO_URI')
    
    if not mongo_uri:
        print("❌ No MONGO_URI found in environment variables")
        print("💡 Make sure your .env file has MONGO_URI set")
        return False
    
    # Hide password in output
    safe_uri = mongo_uri
    if '@' in mongo_uri:
        parts = mongo_uri.split('@')
        if '//' in parts[0]:
            user_part = parts[0].split('//')[1]
            safe_uri = mongo_uri.replace(user_part, '***:***')
    
    print(f"🔗 Connection URI: {safe_uri}")
    
    try:
        # Test connection
        print("🔄 Attempting to connect...")
        client = MongoClient(mongo_uri, serverSelectionTimeoutMS=10000)
        
        # Test the connection
        print("🔄 Testing server connection...")
        client.admin.command('ping')
        print("✅ Successfully connected to MongoDB Atlas!")
        
        # Test database access
        print("🔄 Testing database access...")
        db = client['sisarasa']
        
        # Test collections
        print("🔄 Testing collections...")
        collections = db.list_collection_names()
        print(f"📁 Available collections: {collections}")
        
        # Test users collection
        users_count = db.users.count_documents({})
        print(f"👥 Users in database: {users_count}")
        
        # Test recipes collection
        recipes_count = db.recipes.count_documents({})
        print(f"🍽️ Recipes in database: {recipes_count}")
        
        # Test write access by inserting a test document
        print("🔄 Testing write access...")
        test_collection = db.test_connection
        test_doc = {"test": True, "message": "SisaRasa connection test"}
        result = test_collection.insert_one(test_doc)
        print(f"✅ Write test successful! Document ID: {result.inserted_id}")
        
        # Clean up test document
        test_collection.delete_one({"_id": result.inserted_id})
        print("🧹 Cleaned up test document")
        
        client.close()
        print("\n🎉 MongoDB Atlas connection is working perfectly!")
        return True
        
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        print(f"❌ Error type: {type(e).__name__}")
        
        # Provide helpful error messages
        error_str = str(e).lower()
        if 'dns' in error_str or 'name does not exist' in error_str:
            print("\n💡 DNS Error - Possible solutions:")
            print("   1. Check if your cluster name is correct")
            print("   2. Verify the cluster is running (not paused)")
            print("   3. Check your internet connection")
        elif 'authentication failed' in error_str:
            print("\n💡 Authentication Error - Possible solutions:")
            print("   1. Check your username and password")
            print("   2. Verify the user has proper permissions")
            print("   3. Make sure special characters in password are URL-encoded")
        elif 'timeout' in error_str:
            print("\n💡 Timeout Error - Possible solutions:")
            print("   1. Check your network access settings in Atlas")
            print("   2. Add your IP address to the whitelist")
            print("   3. Try 'Allow access from anywhere' for testing")
        
        return False

def create_sample_user():
    """Create a sample user for testing"""
    print("\n👤 Creating sample user for testing...")
    
    load_dotenv()
    mongo_uri = os.getenv('MONGO_URI')
    
    try:
        client = MongoClient(mongo_uri)
        db = client['sisarasa']
        
        # Check if test user already exists
        existing_user = db.users.find_one({"email": "<EMAIL>"})
        if existing_user:
            print("ℹ️  Test user already exists")
            return True
        
        # Create test user
        import bcrypt
        from datetime import datetime
        
        password = "password123"
        hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())
        
        test_user = {
            "name": "Test User",
            "email": "<EMAIL>",
            "password": hashed_password,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "saved_recipes": [],
            "search_history": [],
            "analytics": {
                "total_recipe_views": 0,
                "total_recipe_saves": 0,
                "total_reviews_given": 0
            }
        }
        
        result = db.users.insert_one(test_user)
        print(f"✅ Test user created! ID: {result.inserted_id}")
        print("📧 Email: <EMAIL>")
        print("🔑 Password: password123")
        
        client.close()
        return True
        
    except Exception as e:
        print(f"❌ Failed to create test user: {e}")
        return False

def main():
    """Main function"""
    print("🧪 MONGODB ATLAS CONNECTION TEST")
    print("=" * 50)
    
    # Test connection
    connection_success = test_mongodb_connection()
    
    if connection_success:
        # Create sample user
        create_sample_user()
        
        print("\n" + "=" * 50)
        print("🎉 MONGODB ATLAS SETUP COMPLETE!")
        print("=" * 50)
        print("✅ Connection working")
        print("✅ Database accessible")
        print("✅ Read/write permissions confirmed")
        print("✅ Test user created")
        print("\n🚀 Your SisaRasa app is ready to use MongoDB Atlas!")
        print("\n📋 Next steps:")
        print("1. Restart your SisaRasa server")
        print("2. Test login with: <EMAIL> / password123")
        print("3. Test save recipe functionality")
    else:
        print("\n" + "=" * 50)
        print("❌ MONGODB ATLAS SETUP FAILED")
        print("=" * 50)
        print("Please follow the setup guide above to fix the connection.")

if __name__ == "__main__":
    main()
