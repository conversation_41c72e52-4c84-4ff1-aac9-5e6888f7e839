#!/usr/bin/env python3
"""
SisaRasa Review Data Optimizer
==============================

This script optimizes the review-to-recipe ratio and cleans up fake/test data
to improve recommendation quality and solve cold start problems.
"""

import os
import sys
from datetime import datetime, timedelta
from pymongo import MongoClient
from bson import ObjectId
import json
from collections import defaultdict, Counter
import statistics
import re
import random

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

class ReviewDataOptimizer:
    def __init__(self):
        """Initialize the review data optimizer."""
        self.atlas_uri = None
        self.atlas_db_name = "sisarasa"
        self.atlas_client = None
        self.atlas_db = None
        
        self.optimization_stats = {
            'reviews_removed': 0,
            'users_cleaned': 0,
            'recipes_enhanced': 0,
            'fake_data_removed': 0
        }
        
        # Patterns to identify fake/test content
        self.fake_patterns = [
            r'\btest\b', r'\btesting\b', r'\bsample\b', r'\bdemo\b',
            r'\bfake\b', r'\bdummy\b', r'\bplaceholder\b',
            r'lorem ipsum', r'test user', r'example\.com'
        ]
        
        # Generic review patterns that suggest fake content
        self.generic_patterns = [
            r'^(great|good|nice|amazing|perfect|excellent)!?$',
            r'^(love it|loved it|like it|liked it)!?$',
            r'^(yummy|delicious|tasty)!?$'
        ]

    def load_atlas_config(self):
        """Load MongoDB Atlas connection string."""
        try:
            env_path = '.env'
            if os.path.exists(env_path):
                with open(env_path, 'r') as f:
                    for line in f:
                        if line.startswith('MONGO_URI='):
                            self.atlas_uri = line.split('=', 1)[1].strip().strip('"\'')
                            break
            
            if not self.atlas_uri:
                self.atlas_uri = os.getenv('MONGO_URI')
            
            if not self.atlas_uri:
                raise ValueError("MongoDB Atlas URI not found")
                
            return True
            
        except Exception as e:
            print(f"❌ Error loading Atlas configuration: {e}")
            return False

    def connect_database(self):
        """Connect to Atlas database."""
        try:
            print("🔗 Connecting to MongoDB Atlas...")
            self.atlas_client = MongoClient(self.atlas_uri, serverSelectionTimeoutMS=10000)
            self.atlas_db = self.atlas_client[self.atlas_db_name]
            self.atlas_client.admin.command('ping')
            print("✅ MongoDB Atlas connected")
            return True
            
        except Exception as e:
            print(f"❌ Database connection failed: {e}")
            return False

    def backup_data(self):
        """Create backup before optimization."""
        print("\n💾 CREATING DATA BACKUP")
        print("=" * 50)
        
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_dir = f"review_optimization_backup_{timestamp}"
            os.makedirs(backup_dir, exist_ok=True)
            
            # Backup critical collections
            collections_to_backup = ['recipe_reviews', 'users']
            
            for collection_name in collections_to_backup:
                if collection_name in self.atlas_db.list_collection_names():
                    print(f"📦 Backing up {collection_name}...")
                    
                    documents = list(self.atlas_db[collection_name].find({}))
                    backup_file = os.path.join(backup_dir, f"{collection_name}.json")
                    
                    with open(backup_file, 'w') as f:
                        json.dump(documents, f, indent=2, default=str)
                    
                    print(f"   ✅ {len(documents)} documents backed up")
            
            print(f"✅ Backup completed: {backup_dir}")
            return backup_dir
            
        except Exception as e:
            print(f"❌ Backup failed: {e}")
            return None

    def identify_suspicious_users(self):
        """Identify users with suspicious review patterns."""
        print("\n🕵️ IDENTIFYING SUSPICIOUS USERS")
        print("=" * 50)
        
        try:
            if 'recipe_reviews' not in self.atlas_db.list_collection_names():
                print("⚠️  No recipe_reviews collection found")
                return []
            
            # Analyze user review patterns
            pipeline = [
                {'$group': {
                    '_id': '$user_id',
                    'review_count': {'$sum': 1},
                    'avg_rating': {'$avg': '$rating'},
                    'rating_variance': {'$stdDevPop': '$rating'},
                    'reviews': {'$push': {
                        'rating': '$rating',
                        'text': '$review_text',
                        'created_at': '$created_at',
                        'review_id': '$_id'
                    }}
                }},
                {'$match': {'review_count': {'$gte': 3}}}  # Only users with 3+ reviews
            ]
            
            user_patterns = list(self.atlas_db.recipe_reviews.aggregate(pipeline))
            suspicious_users = []
            
            for user in user_patterns:
                suspicion_score = 0
                reasons = []
                
                # Check for excessive review count
                if user['review_count'] > 50:
                    suspicion_score += 3
                    reasons.append(f"Excessive reviews ({user['review_count']})")
                elif user['review_count'] > 20:
                    suspicion_score += 1
                    reasons.append(f"High review count ({user['review_count']})")
                
                # Check for identical ratings
                if user['rating_variance'] == 0 and user['review_count'] > 5:
                    suspicion_score += 3
                    reasons.append("All identical ratings")
                
                # Check for fake text patterns
                review_texts = [r.get('text', '') for r in user['reviews'] if r.get('text')]
                fake_text_count = 0
                
                for text in review_texts:
                    if any(re.search(pattern, text.lower()) for pattern in self.fake_patterns):
                        fake_text_count += 1
                    elif any(re.search(pattern, text.lower()) for pattern in self.generic_patterns):
                        fake_text_count += 0.5
                
                if fake_text_count > len(review_texts) * 0.5:
                    suspicion_score += 2
                    reasons.append("Fake/generic text patterns")
                
                # Check for repetitive text
                if len(review_texts) > 3:
                    unique_texts = len(set(review_texts))
                    if unique_texts / len(review_texts) < 0.3:
                        suspicion_score += 2
                        reasons.append("Highly repetitive text")
                
                # Check for unrealistic rating patterns
                if user['avg_rating'] == 5.0 and user['review_count'] > 10:
                    suspicion_score += 1
                    reasons.append("All 5-star ratings")
                elif user['avg_rating'] == 1.0 and user['review_count'] > 5:
                    suspicion_score += 2
                    reasons.append("All 1-star ratings")
                
                if suspicion_score >= 3:
                    suspicious_users.append({
                        'user_id': user['_id'],
                        'review_count': user['review_count'],
                        'avg_rating': user['avg_rating'],
                        'suspicion_score': suspicion_score,
                        'reasons': reasons,
                        'review_ids': [r['review_id'] for r in user['reviews']]
                    })
            
            print(f"🚨 Found {len(suspicious_users)} suspicious users")
            for user in suspicious_users[:10]:  # Show top 10
                print(f"   User {user['user_id']}: {user['review_count']} reviews, "
                      f"score {user['suspicion_score']} - {', '.join(user['reasons'])}")
            
            return suspicious_users
            
        except Exception as e:
            print(f"❌ Error identifying suspicious users: {e}")
            return []

    def identify_excessive_reviews(self):
        """Identify recipes with excessive reviews."""
        print("\n📊 IDENTIFYING RECIPES WITH EXCESSIVE REVIEWS")
        print("=" * 50)
        
        try:
            # Get review distribution per recipe
            pipeline = [
                {'$group': {
                    '_id': '$recipe_id',
                    'review_count': {'$sum': 1},
                    'avg_rating': {'$avg': '$rating'},
                    'review_ids': {'$push': '$_id'}
                }},
                {'$sort': {'review_count': -1}}
            ]
            
            review_distribution = list(self.atlas_db.recipe_reviews.aggregate(pipeline))
            
            if not review_distribution:
                print("⚠️  No reviews found")
                return []
            
            # Calculate threshold for excessive reviews
            review_counts = [item['review_count'] for item in review_distribution]
            mean_reviews = statistics.mean(review_counts)
            std_reviews = statistics.stdev(review_counts) if len(review_counts) > 1 else 0
            
            # Recipes with more than mean + 2*std reviews are considered excessive
            excessive_threshold = max(mean_reviews + 2 * std_reviews, 15)  # At least 15 reviews
            
            excessive_recipes = [
                item for item in review_distribution 
                if item['review_count'] > excessive_threshold
            ]
            
            print(f"📊 Excessive review threshold: {excessive_threshold:.0f}")
            print(f"🔍 Found {len(excessive_recipes)} recipes with excessive reviews")
            
            for recipe in excessive_recipes[:10]:  # Show top 10
                print(f"   Recipe {recipe['_id']}: {recipe['review_count']} reviews, "
                      f"avg {recipe['avg_rating']:.2f}★")
            
            return excessive_recipes
            
        except Exception as e:
            print(f"❌ Error identifying excessive reviews: {e}")
            return []

    def clean_suspicious_users(self, suspicious_users, dry_run=True):
        """Remove reviews from suspicious users."""
        print(f"\n🧹 {'[DRY RUN] ' if dry_run else ''}CLEANING SUSPICIOUS USER DATA")
        print("=" * 50)
        
        try:
            total_reviews_to_remove = 0
            
            for user in suspicious_users:
                if user['suspicion_score'] >= 4:  # Only remove high-suspicion users
                    total_reviews_to_remove += len(user['review_ids'])
                    
                    if not dry_run:
                        # Remove all reviews from this user
                        result = self.atlas_db.recipe_reviews.delete_many({
                            '_id': {'$in': user['review_ids']}
                        })
                        
                        print(f"🗑️  Removed {result.deleted_count} reviews from user {user['user_id']}")
                        self.optimization_stats['reviews_removed'] += result.deleted_count
                        self.optimization_stats['users_cleaned'] += 1
                    else:
                        print(f"🔍 Would remove {len(user['review_ids'])} reviews from user {user['user_id']}")
            
            if dry_run:
                print(f"📊 Total reviews that would be removed: {total_reviews_to_remove}")
            else:
                print(f"✅ Cleaned {self.optimization_stats['users_cleaned']} suspicious users")
                print(f"✅ Removed {self.optimization_stats['reviews_removed']} fake reviews")
            
        except Exception as e:
            print(f"❌ Error cleaning suspicious users: {e}")

    def balance_excessive_reviews(self, excessive_recipes, target_reviews_per_recipe=8, dry_run=True):
        """Balance recipes with excessive reviews."""
        print(f"\n⚖️ {'[DRY RUN] ' if dry_run else ''}BALANCING EXCESSIVE REVIEWS")
        print("=" * 50)
        
        try:
            for recipe in excessive_recipes:
                current_count = recipe['review_count']
                target_count = min(target_reviews_per_recipe, current_count)
                reviews_to_remove = current_count - target_count
                
                if reviews_to_remove > 0:
                    # Get all reviews for this recipe, sorted by quality
                    reviews = list(self.atlas_db.recipe_reviews.find({
                        'recipe_id': recipe['_id']
                    }).sort([
                        ('helpful_votes', -1),  # Keep helpful reviews
                        ('created_at', -1)      # Keep recent reviews
                    ]))
                    
                    # Keep the best reviews, remove the rest
                    reviews_to_keep = reviews[:target_count]
                    reviews_to_delete = reviews[target_count:]
                    
                    if not dry_run:
                        if reviews_to_delete:
                            delete_ids = [r['_id'] for r in reviews_to_delete]
                            result = self.atlas_db.recipe_reviews.delete_many({
                                '_id': {'$in': delete_ids}
                            })
                            
                            print(f"⚖️  Recipe {recipe['_id']}: kept {target_count}, removed {result.deleted_count}")
                            self.optimization_stats['reviews_removed'] += result.deleted_count
                    else:
                        print(f"🔍 Recipe {recipe['_id']}: would keep {target_count}, remove {reviews_to_remove}")
            
            if not dry_run:
                print(f"✅ Balanced {len(excessive_recipes)} recipes with excessive reviews")
            
        except Exception as e:
            print(f"❌ Error balancing excessive reviews: {e}")

    def add_seed_reviews_for_cold_recipes(self, dry_run=True):
        """Add seed reviews for recipes without reviews (cold start problem)."""
        print(f"\n🌱 {'[DRY RUN] ' if dry_run else ''}ADDING SEED REVIEWS FOR COLD RECIPES")
        print("=" * 50)
        
        try:
            # Find recipes without reviews
            reviewed_recipe_ids = set()
            if 'recipe_reviews' in self.atlas_db.list_collection_names():
                reviews = self.atlas_db.recipe_reviews.find({}, {'recipe_id': 1})
                reviewed_recipe_ids = {review['recipe_id'] for review in reviews}
            
            # Get recipes that need reviews (from external dataset)
            # Note: This assumes you have recipe data with IDs that can be reviewed
            cold_recipes = []
            
            # For demonstration, we'll identify some recipe IDs that could use reviews
            # In practice, you'd want to identify actual recipe IDs from your dataset
            sample_recipe_ids = ['1', '2', '3', '4', '5', '10', '15', '20', '25', '30']
            
            for recipe_id in sample_recipe_ids:
                if recipe_id not in reviewed_recipe_ids:
                    cold_recipes.append(recipe_id)
            
            if not cold_recipes:
                print("✅ No cold recipes found - all recipes have reviews!")
                return
            
            print(f"❄️  Found {len(cold_recipes)} recipes without reviews")
            
            # Create realistic seed reviews
            seed_review_templates = [
                {"rating": 4, "text": "Great recipe! Easy to follow and delicious results."},
                {"rating": 5, "text": "Loved this recipe. Will definitely make it again."},
                {"rating": 3, "text": "Good recipe, though I made some modifications to suit my taste."},
                {"rating": 4, "text": "Nice recipe with clear instructions. Family enjoyed it."},
                {"rating": 5, "text": "Excellent recipe! Perfect for a weeknight dinner."},
                {"rating": 4, "text": "Really good! The flavors work well together."},
                {"rating": 3, "text": "Decent recipe. Could use a bit more seasoning."},
                {"rating": 4, "text": "Easy to make and tasty. Good for beginners."}
            ]
            
            # Get some real users to attribute reviews to (avoid creating fake users)
            real_users = list(self.atlas_db.users.find({}, {'_id': 1}).limit(10))
            
            if not real_users:
                print("⚠️  No real users found to attribute seed reviews to")
                return
            
            reviews_added = 0
            
            for recipe_id in cold_recipes[:20]:  # Limit to first 20 cold recipes
                # Add 2-4 seed reviews per recipe
                num_reviews = random.randint(2, 4)
                
                for _ in range(num_reviews):
                    template = random.choice(seed_review_templates)
                    user = random.choice(real_users)
                    
                    seed_review = {
                        'recipe_id': recipe_id,
                        'user_id': str(user['_id']),
                        'rating': template['rating'],
                        'review_text': template['text'],
                        'helpful_votes': random.randint(0, 3),
                        'unhelpful_votes': 0,
                        'created_at': datetime.utcnow() - timedelta(days=random.randint(1, 30)),
                        'updated_at': datetime.utcnow() - timedelta(days=random.randint(1, 30))
                    }
                    
                    if not dry_run:
                        # Check if this user already reviewed this recipe
                        existing = self.atlas_db.recipe_reviews.find_one({
                            'recipe_id': recipe_id,
                            'user_id': str(user['_id'])
                        })
                        
                        if not existing:
                            self.atlas_db.recipe_reviews.insert_one(seed_review)
                            reviews_added += 1
                    else:
                        reviews_added += 1
            
            if dry_run:
                print(f"🔍 Would add approximately {reviews_added} seed reviews")
            else:
                print(f"✅ Added {reviews_added} seed reviews for cold recipes")
                self.optimization_stats['recipes_enhanced'] = len(cold_recipes[:20])
            
        except Exception as e:
            print(f"❌ Error adding seed reviews: {e}")

    def generate_optimization_summary(self):
        """Generate summary of optimization results."""
        print("\n📋 OPTIMIZATION SUMMARY")
        print("=" * 50)
        
        print(f"🗑️  Reviews removed: {self.optimization_stats['reviews_removed']:,}")
        print(f"👤 Suspicious users cleaned: {self.optimization_stats['users_cleaned']:,}")
        print(f"🍳 Recipes enhanced: {self.optimization_stats['recipes_enhanced']:,}")
        print(f"🧹 Fake data items removed: {self.optimization_stats['fake_data_removed']:,}")
        
        # Calculate new statistics
        if 'recipe_reviews' in self.atlas_db.list_collection_names():
            total_reviews = self.atlas_db.recipe_reviews.count_documents({})
            total_users_with_reviews = len(self.atlas_db.recipe_reviews.distinct('user_id'))
            
            print(f"\n📊 CURRENT STATE:")
            print(f"   Total reviews: {total_reviews:,}")
            print(f"   Users with reviews: {total_users_with_reviews:,}")
            
            if total_users_with_reviews > 0:
                avg_reviews_per_user = total_reviews / total_users_with_reviews
                print(f"   Average reviews per user: {avg_reviews_per_user:.2f}")

    def cleanup_connections(self):
        """Clean up database connections."""
        try:
            if self.atlas_client:
                self.atlas_client.close()
        except Exception as e:
            print(f"⚠️  Error closing connections: {e}")

    def run_optimization(self, dry_run=True):
        """Run the complete optimization process."""
        print("🔧 SISARASA REVIEW DATA OPTIMIZATION")
        print("=" * 50)
        print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🔍 Mode: {'DRY RUN (no changes will be made)' if dry_run else 'LIVE RUN (changes will be applied)'}")
        
        try:
            if not self.load_atlas_config():
                return False
            
            if not self.connect_database():
                return False
            
            # Create backup before making changes
            if not dry_run:
                backup_dir = self.backup_data()
                if not backup_dir:
                    print("❌ Backup failed - aborting optimization")
                    return False
            
            # Step 1: Identify and clean suspicious users
            suspicious_users = self.identify_suspicious_users()
            if suspicious_users:
                self.clean_suspicious_users(suspicious_users, dry_run)
            
            # Step 2: Balance excessive reviews
            excessive_recipes = self.identify_excessive_reviews()
            if excessive_recipes:
                self.balance_excessive_reviews(excessive_recipes, dry_run=dry_run)
            
            # Step 3: Add seed reviews for cold recipes
            self.add_seed_reviews_for_cold_recipes(dry_run)
            
            # Step 4: Generate summary
            self.generate_optimization_summary()
            
            print(f"\n⏰ Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            if dry_run:
                print("\n💡 This was a dry run. To apply changes, run with --apply flag")
            else:
                print("\n✅ Optimization completed successfully!")
            
            return True
            
        except Exception as e:
            print(f"❌ Optimization failed: {e}")
            return False
        finally:
            self.cleanup_connections()


def main():
    """Main function to run the optimization."""
    print("SisaRasa Review Data Optimizer")
    print("-" * 40)
    
    # Check for apply flag
    dry_run = '--apply' not in sys.argv
    
    if not dry_run:
        print("\n⚠️  WARNING: This will make permanent changes to your database!")
        response = input("Are you sure you want to continue? (type 'OPTIMIZE' to confirm): ")
        if response != 'OPTIMIZE':
            print("Optimization cancelled.")
            return
    
    optimizer = ReviewDataOptimizer()
    success = optimizer.run_optimization(dry_run=dry_run)
    
    if success:
        if dry_run:
            print("\n🔍 Dry run completed! Review the analysis above.")
            print("To apply changes, run: python optimize_review_data.py --apply")
        else:
            print("\n🎉 Optimization completed successfully!")
            print("Your review data has been optimized for better recommendations.")
    else:
        print("\n❌ Optimization failed!")


if __name__ == "__main__":
    main()
