#!/usr/bin/env python3
"""
Test script to verify login authentication and save recipe functionality
"""

import requests
import json
import time

# Test configuration
BASE_URL = "http://127.0.0.1:5000"
TEST_USER = {
    "email": "<EMAIL>",
    "password": "password123"
}

def test_login():
    """Test login functionality"""
    print("🔐 Testing Login Authentication...")
    
    login_url = f"{BASE_URL}/api/auth/login"
    
    try:
        response = requests.post(login_url, json=TEST_USER, timeout=10)
        print(f"Login status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Login successful!")
            print(f"User: {data.get('user', {}).get('name', 'Unknown')}")
            return data.get('token')
        else:
            print(f"❌ Login failed: {response.text}")
            return None
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection refused - server not running")
        return None
    except Exception as e:
        print(f"❌ Login error: {e}")
        return None

def test_save_recipe(token):
    """Test save recipe functionality"""
    if not token:
        print("❌ Cannot test save recipe - no valid token")
        return False
        
    print("\n💾 Testing Save Recipe Functionality...")
    
    # Test recipe data
    test_recipe = {
        "original_id": 12345,
        "name": "Test Recipe",
        "ingredients": ["egg", "rice", "salt"],
        "instructions": ["Mix ingredients", "Cook for 10 minutes"],
        "prep_time": 15,
        "cook_time": 20,
        "servings": 4
    }
    
    save_url = f"{BASE_URL}/api/recipe/12345/save"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(save_url, headers=headers, timeout=10)
        print(f"Save recipe status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Recipe saved successfully!")
            print(f"Message: {data.get('message', 'No message')}")
            return True
        else:
            print(f"❌ Save recipe failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Save recipe error: {e}")
        return False

def test_get_saved_recipes(token):
    """Test getting saved recipes"""
    if not token:
        print("❌ Cannot test get saved recipes - no valid token")
        return False
        
    print("\n📋 Testing Get Saved Recipes...")
    
    saved_url = f"{BASE_URL}/api/recipes/saved"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(saved_url, headers=headers, timeout=10)
        print(f"Get saved recipes status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            recipes = data.get('recipes', [])
            print(f"✅ Retrieved {len(recipes)} saved recipes")
            if recipes:
                print(f"Sample recipe: {recipes[0].get('name', 'Unknown')}")
            return True
        else:
            print(f"❌ Get saved recipes failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Get saved recipes error: {e}")
        return False

def main():
    """Main test function"""
    print("=" * 60)
    print("🧪 TESTING SISARASA LOGIN & SAVE RECIPE FUNCTIONALITY")
    print("=" * 60)
    
    # Test login
    token = test_login()
    
    if token:
        # Test save recipe
        save_success = test_save_recipe(token)
        
        # Test get saved recipes
        get_success = test_get_saved_recipes(token)
        
        print("\n" + "=" * 60)
        print("📊 TEST RESULTS SUMMARY")
        print("=" * 60)
        print(f"✅ Login: {'PASS' if token else 'FAIL'}")
        print(f"✅ Save Recipe: {'PASS' if save_success else 'FAIL'}")
        print(f"✅ Get Saved Recipes: {'PASS' if get_success else 'FAIL'}")
        
        if token and save_success and get_success:
            print("\n🎉 ALL TESTS PASSED! Authentication and save recipe functionality working correctly.")
        else:
            print("\n⚠️  Some tests failed. Check the output above for details.")
    else:
        print("\n❌ Login failed - cannot proceed with other tests")

if __name__ == "__main__":
    main()
