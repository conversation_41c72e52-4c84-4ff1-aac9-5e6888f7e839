#!/usr/bin/env python3
"""
Quick script to replace all mongo.db references with mongo_db
"""

import re

def fix_mongo_references():
    """Replace all mongo.db references with mongo_db in user.py"""
    
    # Read the file
    with open('src/api/models/user.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Replace all mongo.db with mongo_db
    updated_content = content.replace('mongo.db', 'mongo_db')
    
    # Write back to file
    with open('src/api/models/user.py', 'w', encoding='utf-8') as f:
        f.write(updated_content)
    
    print("✅ All mongo.db references replaced with mongo_db")

if __name__ == "__main__":
    fix_mongo_references()
