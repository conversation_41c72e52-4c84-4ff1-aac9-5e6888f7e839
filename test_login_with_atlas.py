#!/usr/bin/env python3
"""
Test login functionality with MongoDB Atlas
"""

import requests
import json

# Test configuration
BASE_URL = "http://127.0.0.1:5000"

def test_login():
    """Test login with the test user we created"""
    print("🔐 Testing Login with MongoDB Atlas...")
    print("=" * 50)
    
    login_url = f"{BASE_URL}/api/auth/login"
    login_data = {
        "email": "<EMAIL>",
        "password": "password123"
    }
    
    try:
        print("🔄 Attempting login...")
        print(f"📧 Email: {login_data['email']}")
        print(f"🔑 Password: {login_data['password']}")
        
        response = requests.post(login_url, json=login_data, timeout=10)
        print(f"📊 Status Code: {response.status_code}")
        print(f"📄 Response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            token = data.get('token')  # Changed from 'access_token' to 'token'
            user_info = data.get('user', {})
            
            print("✅ LOGIN SUCCESSFUL!")
            print(f"👤 User: {user_info.get('name', 'Unknown')}")
            print(f"📧 Email: {user_info.get('email', 'Unknown')}")
            print(f"🎫 Token: {token[:50]}..." if token else "No token")
            
            return token
        else:
            print("❌ LOGIN FAILED!")
            return None
            
    except Exception as e:
        print(f"❌ Login error: {e}")
        return None

def test_save_recipe_with_login(token):
    """Test save recipe functionality with real login token"""
    if not token:
        print("⚠️  Skipping save recipe test - no valid token")
        return False
    
    print("\n💾 Testing Save Recipe with Real Login Token...")
    print("=" * 50)
    
    # First, get a recipe to save
    print("🔍 Getting a recipe to save...")
    recommend_url = f"{BASE_URL}/api/recommend"
    search_data = {
        "ingredients": ["chicken", "rice"],
        "limit": 1
    }
    
    try:
        response = requests.post(recommend_url, json=search_data, timeout=10)
        if response.status_code == 200:
            data = response.json()
            recipes = data.get('recipes', [])
            if recipes:
                recipe = recipes[0]
                recipe_id = recipe.get('id')
                recipe_name = recipe.get('name', 'Unknown')
                print(f"📖 Found recipe: {recipe_name} (ID: {recipe_id})")
                
                # Now try to save it
                save_url = f"{BASE_URL}/api/recipe/{recipe_id}/save"
                headers = {
                    "Authorization": f"Bearer {token}",
                    "Content-Type": "application/json"
                }
                
                save_response = requests.post(save_url, headers=headers, timeout=10)
                print(f"💾 Save status: {save_response.status_code}")
                print(f"📄 Save response: {save_response.text}")
                
                if save_response.status_code == 200:
                    print("✅ RECIPE SAVED SUCCESSFULLY!")
                    return True
                else:
                    print("❌ RECIPE SAVE FAILED!")
                    return False
            else:
                print("❌ No recipes found to save")
                return False
        else:
            print("❌ Failed to get recipes")
            return False
            
    except Exception as e:
        print(f"❌ Save recipe error: {e}")
        return False

def test_get_saved_recipes(token):
    """Test getting saved recipes with real login token"""
    if not token:
        print("⚠️  Skipping get saved recipes test - no valid token")
        return False
    
    print("\n📋 Testing Get Saved Recipes with Real Login Token...")
    print("=" * 50)
    
    saved_url = f"{BASE_URL}/api/recipes/saved"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(saved_url, headers=headers, timeout=10)
        print(f"📊 Status Code: {response.status_code}")
        print(f"📄 Response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            recipes = data.get('recipes', [])
            count = data.get('count', 0)
            
            print(f"✅ RETRIEVED {count} SAVED RECIPES!")
            if recipes:
                for i, recipe in enumerate(recipes[:3]):  # Show first 3
                    print(f"  {i+1}. {recipe.get('name', 'Unknown')}")
            return True
        else:
            print("❌ GET SAVED RECIPES FAILED!")
            return False
            
    except Exception as e:
        print(f"❌ Get saved recipes error: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 COMPLETE LOGIN AND SAVE RECIPE TEST WITH MONGODB ATLAS")
    print("=" * 70)
    
    # Test 1: Login
    token = test_login()
    
    # Test 2: Save recipe (if login successful)
    save_success = test_save_recipe_with_login(token)
    
    # Test 3: Get saved recipes (if login successful)
    get_saved_success = test_get_saved_recipes(token)
    
    print("\n" + "=" * 70)
    print("📊 FINAL TEST RESULTS")
    print("=" * 70)
    print(f"🔐 Login: {'✅ PASS' if token else '❌ FAIL'}")
    print(f"💾 Save Recipe: {'✅ PASS' if save_success else '❌ FAIL'}")
    print(f"📋 Get Saved Recipes: {'✅ PASS' if get_saved_success else '❌ FAIL'}")
    
    if token and save_success and get_saved_success:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ MongoDB Atlas connection is working")
        print("✅ User authentication is working")
        print("✅ Save recipe functionality is working")
        print("✅ Get saved recipes functionality is working")
        print("\n🚀 Your SisaRasa system is fully operational!")
    else:
        print("\n⚠️  Some tests failed. Check the MongoDB connection and server logs.")

if __name__ == "__main__":
    main()
