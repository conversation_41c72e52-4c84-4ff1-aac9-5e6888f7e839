#!/usr/bin/env python3
"""
MongoDB Migration Rollback Script
=================================

This script provides rollback functionality for the MongoDB migration.
It can clear Atlas data or restore from backup if needed.

⚠️  WARNING: This script can delete data. Use with extreme caution!
"""

import os
import sys
from datetime import datetime
from pymongo import MongoClient
import json

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

class MigrationRollback:
    def __init__(self):
        """Initialize the rollback tool."""
        self.atlas_uri = None
        self.atlas_db_name = "sisarasa"
        self.atlas_client = None
        self.atlas_db = None

    def load_atlas_config(self):
        """Load MongoDB Atlas connection string."""
        try:
            env_path = '.env'
            if os.path.exists(env_path):
                with open(env_path, 'r') as f:
                    for line in f:
                        if line.startswith('MONGO_URI='):
                            self.atlas_uri = line.split('=', 1)[1].strip().strip('"\'')
                            break
            
            if not self.atlas_uri:
                self.atlas_uri = os.getenv('MONGO_URI')
            
            if not self.atlas_uri:
                raise ValueError("MongoDB Atlas URI not found")
                
            return True
            
        except Exception as e:
            print(f"❌ Error loading Atlas configuration: {e}")
            return False

    def connect_atlas(self):
        """Connect to Atlas database."""
        try:
            print("🔗 Connecting to MongoDB Atlas...")
            self.atlas_client = MongoClient(self.atlas_uri, serverSelectionTimeoutMS=10000)
            self.atlas_db = self.atlas_client[self.atlas_db_name]
            self.atlas_client.admin.command('ping')
            print("✅ MongoDB Atlas connected")
            return True
            
        except Exception as e:
            print(f"❌ Atlas connection failed: {e}")
            return False

    def backup_atlas_data(self):
        """Create a backup of current Atlas data before rollback."""
        print("\n💾 CREATING BACKUP OF ATLAS DATA")
        print("=" * 50)
        
        try:
            backup_dir = f"atlas_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            os.makedirs(backup_dir, exist_ok=True)
            
            collections = self.atlas_db.list_collection_names()
            
            for collection_name in collections:
                if not collection_name.startswith('system.'):
                    print(f"📦 Backing up {collection_name}...")
                    
                    documents = list(self.atlas_db[collection_name].find({}))
                    
                    backup_file = os.path.join(backup_dir, f"{collection_name}.json")
                    with open(backup_file, 'w') as f:
                        json.dump(documents, f, indent=2, default=str)
                    
                    print(f"   ✅ {len(documents)} documents backed up")
            
            print(f"\n✅ Backup completed in directory: {backup_dir}")
            return backup_dir
            
        except Exception as e:
            print(f"❌ Backup failed: {e}")
            return None

    def clear_atlas_collections(self, collections_to_clear=None):
        """Clear specified collections from Atlas."""
        print("\n🗑️  CLEARING ATLAS COLLECTIONS")
        print("=" * 50)
        
        try:
            if collections_to_clear is None:
                collections_to_clear = [
                    'users', 'recipes', 'reviews', 'community_posts',
                    'community_recipes', 'verifications', 'post_comments'
                ]
            
            for collection_name in collections_to_clear:
                if collection_name in self.atlas_db.list_collection_names():
                    count_before = self.atlas_db[collection_name].count_documents({})
                    
                    if count_before > 0:
                        print(f"🗑️  Clearing {collection_name} ({count_before} documents)...")
                        result = self.atlas_db[collection_name].delete_many({})
                        print(f"   ✅ Deleted {result.deleted_count} documents")
                    else:
                        print(f"⚠️  {collection_name} is already empty")
                else:
                    print(f"⚠️  Collection {collection_name} does not exist")
            
            print("\n✅ Collection clearing completed")
            return True
            
        except Exception as e:
            print(f"❌ Error clearing collections: {e}")
            return False

    def restore_from_backup(self, backup_dir):
        """Restore Atlas data from a backup directory."""
        print(f"\n📥 RESTORING FROM BACKUP: {backup_dir}")
        print("=" * 50)
        
        try:
            if not os.path.exists(backup_dir):
                print(f"❌ Backup directory not found: {backup_dir}")
                return False
            
            backup_files = [f for f in os.listdir(backup_dir) if f.endswith('.json')]
            
            for backup_file in backup_files:
                collection_name = backup_file.replace('.json', '')
                file_path = os.path.join(backup_dir, backup_file)
                
                print(f"📦 Restoring {collection_name}...")
                
                with open(file_path, 'r') as f:
                    documents = json.load(f)
                
                if documents:
                    # Convert string ObjectIds back to ObjectId objects
                    for doc in documents:
                        if '_id' in doc and isinstance(doc['_id'], dict) and '$oid' in doc['_id']:
                            doc['_id'] = ObjectId(doc['_id']['$oid'])
                    
                    result = self.atlas_db[collection_name].insert_many(documents)
                    print(f"   ✅ Restored {len(result.inserted_ids)} documents")
                else:
                    print(f"   ⚠️  No documents to restore for {collection_name}")
            
            print("\n✅ Restore completed")
            return True
            
        except Exception as e:
            print(f"❌ Restore failed: {e}")
            return False

    def show_atlas_status(self):
        """Show current status of Atlas database."""
        print("\n📊 ATLAS DATABASE STATUS")
        print("=" * 50)
        
        try:
            collections = self.atlas_db.list_collection_names()
            
            if not collections:
                print("📭 No collections found in Atlas database")
                return
            
            total_documents = 0
            for collection_name in sorted(collections):
                if not collection_name.startswith('system.'):
                    count = self.atlas_db[collection_name].count_documents({})
                    total_documents += count
                    print(f"📦 {collection_name}: {count:,} documents")
            
            print(f"\n📊 Total documents: {total_documents:,}")
            
        except Exception as e:
            print(f"❌ Error getting Atlas status: {e}")

    def cleanup_connections(self):
        """Clean up database connections."""
        try:
            if self.atlas_client:
                self.atlas_client.close()
        except Exception as e:
            print(f"⚠️  Error closing connections: {e}")

    def run_rollback(self, action):
        """Run the rollback process."""
        print("🔄 MONGODB MIGRATION ROLLBACK")
        print("=" * 50)
        print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        try:
            if not self.load_atlas_config():
                return False
            
            if not self.connect_atlas():
                return False
            
            if action == 'status':
                self.show_atlas_status()
            
            elif action == 'backup':
                backup_dir = self.backup_atlas_data()
                if backup_dir:
                    print(f"\n✅ Backup created: {backup_dir}")
                    return True
                else:
                    return False
            
            elif action == 'clear':
                # Show current status first
                self.show_atlas_status()
                
                # Confirm before clearing
                print("\n⚠️  WARNING: This will delete ALL migrated data from Atlas!")
                response = input("Are you sure you want to continue? (type 'DELETE' to confirm): ")
                
                if response == 'DELETE':
                    # Create backup first
                    backup_dir = self.backup_atlas_data()
                    if backup_dir:
                        return self.clear_atlas_collections()
                    else:
                        print("❌ Rollback cancelled due to backup failure")
                        return False
                else:
                    print("❌ Rollback cancelled")
                    return False
            
            elif action.startswith('restore:'):
                backup_dir = action.split(':', 1)[1]
                return self.restore_from_backup(backup_dir)
            
            else:
                print(f"❌ Unknown action: {action}")
                return False
            
        except Exception as e:
            print(f"❌ Rollback failed: {e}")
            return False
        finally:
            self.cleanup_connections()


def main():
    """Main function to run rollback operations."""
    print("MongoDB Migration Rollback Tool")
    print("-" * 40)
    
    if len(sys.argv) < 2:
        print("\nUsage:")
        print("  python rollback_migration.py status          # Show Atlas database status")
        print("  python rollback_migration.py backup          # Create backup of Atlas data")
        print("  python rollback_migration.py clear           # Clear all migrated data from Atlas")
        print("  python rollback_migration.py restore:DIR     # Restore from backup directory")
        print("\nExamples:")
        print("  python rollback_migration.py status")
        print("  python rollback_migration.py backup")
        print("  python rollback_migration.py clear")
        print("  python rollback_migration.py restore:atlas_backup_20250126_123456")
        return
    
    action = sys.argv[1]
    
    rollback = MigrationRollback()
    success = rollback.run_rollback(action)
    
    if success:
        print(f"\n✅ Rollback operation '{action}' completed!")
    else:
        print(f"\n❌ Rollback operation '{action}' failed!")


if __name__ == "__main__":
    main()
