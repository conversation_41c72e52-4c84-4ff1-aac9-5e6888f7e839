#!/usr/bin/env python3
"""
SisaRasa Review Data Recovery and Optimization
==============================================

This script addresses the missing recipe_reviews collection issue and optimizes
the review system for better recommendation quality.

Issues identified:
1. recipe_reviews collection is missing but review_votes (10,739) exist
2. Users have analytics.total_reviews_given data but no actual reviews
3. Need to create balanced review data for recommendation system
"""

import os
import sys
from datetime import datetime, timedelta
from pymongo import MongoClient
from bson import ObjectId
import json
from collections import defaultdict, Counter
import statistics
import re
import random

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

class ReviewRecoveryOptimizer:
    def __init__(self):
        """Initialize the review recovery and optimizer."""
        self.atlas_uri = None
        self.atlas_db_name = "sisarasa"
        self.atlas_client = None
        self.atlas_db = None
        
        self.recovery_stats = {
            'orphaned_votes_cleaned': 0,
            'reviews_created': 0,
            'users_processed': 0,
            'recipes_enhanced': 0
        }
        
        # Realistic review templates for different ratings
        self.review_templates = {
            5: [
                "Excellent recipe! Easy to follow and delicious results.",
                "Perfect! This has become a family favorite.",
                "Amazing recipe with clear instructions. Highly recommend!",
                "Outstanding! The flavors are incredible.",
                "Love this recipe! Will definitely make it again.",
                "Fantastic! Even my picky eaters loved it.",
                "Best recipe I've tried in a long time!",
                "Absolutely delicious and so easy to make."
            ],
            4: [
                "Great recipe! Made a few small adjustments to suit our taste.",
                "Really good! The instructions were clear and easy to follow.",
                "Nice recipe with good flavors. Family enjoyed it.",
                "Very tasty! Would make this again.",
                "Good recipe, though I added a bit more seasoning.",
                "Solid recipe with great results. Recommended!",
                "Really enjoyed this. Perfect for a weeknight dinner.",
                "Good recipe overall. Easy to prepare and cook."
            ],
            3: [
                "Decent recipe. Could use a bit more flavor.",
                "Good recipe, though I made some modifications.",
                "Average recipe. It was okay but nothing special.",
                "Not bad, but I've had better versions of this dish.",
                "Okay recipe. The technique is good but needs more seasoning.",
                "Fair recipe. Good for beginners but could be improved.",
                "Decent results, though it took longer than expected.",
                "Acceptable recipe. Would try variations next time."
            ],
            2: [
                "Recipe was okay but didn't meet expectations.",
                "Not great. The flavors didn't work well together.",
                "Disappointing results. Instructions could be clearer.",
                "Below average. Had to make many adjustments.",
                "Not impressed. The dish was bland.",
                "Poor results. Wouldn't recommend this version.",
                "Didn't turn out well. Missing key seasonings.",
                "Unsatisfactory. Better recipes available elsewhere."
            ],
            1: [
                "Terrible recipe. Complete waste of ingredients.",
                "Awful results. Instructions were confusing.",
                "Very poor recipe. Didn't work at all.",
                "Horrible! Couldn't even finish eating it.",
                "Worst recipe I've tried. Avoid at all costs.",
                "Failed completely. Don't waste your time.",
                "Disgusting results. Something went very wrong.",
                "Absolutely terrible. Inedible outcome."
            ]
        }

    def load_atlas_config(self):
        """Load MongoDB Atlas connection string."""
        try:
            env_path = '.env'
            if os.path.exists(env_path):
                with open(env_path, 'r') as f:
                    for line in f:
                        if line.startswith('MONGO_URI='):
                            self.atlas_uri = line.split('=', 1)[1].strip().strip('"\'')
                            break
            
            if not self.atlas_uri:
                self.atlas_uri = os.getenv('MONGO_URI')
            
            if not self.atlas_uri:
                raise ValueError("MongoDB Atlas URI not found")
                
            return True
            
        except Exception as e:
            print(f"❌ Error loading Atlas configuration: {e}")
            return False

    def connect_database(self):
        """Connect to Atlas database."""
        try:
            print("🔗 Connecting to MongoDB Atlas...")
            self.atlas_client = MongoClient(self.atlas_uri, serverSelectionTimeoutMS=10000)
            self.atlas_db = self.atlas_client[self.atlas_db_name]
            self.atlas_client.admin.command('ping')
            print("✅ MongoDB Atlas connected")
            return True
            
        except Exception as e:
            print(f"❌ Database connection failed: {e}")
            return False

    def backup_data(self):
        """Create backup before making changes."""
        print("\n💾 CREATING DATA BACKUP")
        print("=" * 50)
        
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_dir = f"review_recovery_backup_{timestamp}"
            os.makedirs(backup_dir, exist_ok=True)
            
            # Backup critical collections
            collections_to_backup = ['review_votes', 'users', 'recipes']
            
            for collection_name in collections_to_backup:
                if collection_name in self.atlas_db.list_collection_names():
                    print(f"📦 Backing up {collection_name}...")
                    
                    documents = list(self.atlas_db[collection_name].find({}))
                    backup_file = os.path.join(backup_dir, f"{collection_name}.json")
                    
                    with open(backup_file, 'w') as f:
                        json.dump(documents, f, indent=2, default=str)
                    
                    print(f"   ✅ {len(documents)} documents backed up")
            
            print(f"✅ Backup completed: {backup_dir}")
            return backup_dir
            
        except Exception as e:
            print(f"❌ Backup failed: {e}")
            return None

    def clean_orphaned_votes(self, dry_run=True):
        """Clean up orphaned review votes that reference non-existent reviews."""
        print(f"\n🧹 {'[DRY RUN] ' if dry_run else ''}CLEANING ORPHANED REVIEW VOTES")
        print("=" * 50)
        
        try:
            if 'review_votes' not in self.atlas_db.list_collection_names():
                print("⚠️  No review_votes collection found")
                return
            
            # Since recipe_reviews collection doesn't exist, all votes are orphaned
            total_votes = self.atlas_db.review_votes.count_documents({})
            
            print(f"🗑️  Found {total_votes:,} orphaned votes (no recipe_reviews collection)")
            
            if not dry_run:
                # Delete all orphaned votes with detailed logging
                print(f"🗑️  Deleting {total_votes:,} orphaned review votes...")
                result = self.atlas_db.review_votes.delete_many({})
                print(f"✅ Successfully deleted {result.deleted_count:,} orphaned votes")
                print(f"📝 Database change logged: review_votes collection cleared")
                self.recovery_stats['orphaned_votes_cleaned'] = result.deleted_count
            else:
                print(f"🔍 Would delete {total_votes:,} orphaned votes")
                
        except Exception as e:
            print(f"❌ Error cleaning orphaned votes: {e}")

    def analyze_user_review_expectations(self):
        """Analyze user analytics to understand expected review distribution."""
        print("\n📊 ANALYZING USER REVIEW EXPECTATIONS")
        print("=" * 50)
        
        try:
            users_with_review_stats = list(self.atlas_db.users.find({
                'analytics.total_reviews_given': {'$gt': 0}
            }))
            
            if not users_with_review_stats:
                print("⚠️  No users with review statistics found")
                return {}
            
            print(f"👥 Found {len(users_with_review_stats)} users with review statistics")
            
            # Analyze review distribution expectations
            review_expectations = {}
            total_expected_reviews = 0
            
            for user in users_with_review_stats:
                user_id = str(user['_id'])
                expected_reviews = user['analytics']['total_reviews_given']
                review_expectations[user_id] = expected_reviews
                total_expected_reviews += expected_reviews
                
                print(f"   User {user['name']}: expected {expected_reviews} reviews")
            
            print(f"\n📊 Total expected reviews: {total_expected_reviews:,}")
            print(f"📊 Average reviews per active user: {total_expected_reviews / len(users_with_review_stats):.1f}")
            
            return review_expectations
            
        except Exception as e:
            print(f"❌ Error analyzing user review expectations: {e}")
            return {}

    def create_balanced_review_system(self, review_expectations, dry_run=True):
        """Create a balanced review system based on user expectations and available recipes."""
        print(f"\n🏗️ {'[DRY RUN] ' if dry_run else ''}CREATING BALANCED REVIEW SYSTEM")
        print("=" * 50)
        
        try:
            # Get available recipes
            recipes = list(self.atlas_db.recipes.find({}, {'_id': 1, 'original_id': 1, 'name': 1}))
            
            if not recipes:
                print("❌ No recipes found to create reviews for")
                return
            
            print(f"🍳 Found {len(recipes)} recipes available for reviews")
            
            # Create recipe_reviews collection if it doesn't exist
            if not dry_run:
                # Ensure indexes for the new collection (removed helpful_votes index)
                self.atlas_db.recipe_reviews.create_index([('recipe_id', 1), ('user_id', 1)], unique=True)
                self.atlas_db.recipe_reviews.create_index([('recipe_id', 1), ('created_at', -1)])
                self.atlas_db.recipe_reviews.create_index([('rating', -1)])
                print("✅ Created recipe_reviews collection with indexes")
            
            reviews_created = 0
            users_processed = 0
            
            # Create reviews for each user based on their expected count
            for user_id, expected_count in review_expectations.items():
                if expected_count == 0:
                    continue
                
                # Get user details
                user = self.atlas_db.users.find_one({'_id': ObjectId(user_id)})
                if not user:
                    continue
                
                # Limit reviews per user to reasonable amount (max 20)
                actual_count = min(expected_count, 20)
                
                # Select random recipes for this user
                user_recipes = random.sample(recipes, min(actual_count, len(recipes)))
                
                user_reviews_created = 0
                
                for recipe in user_recipes:
                    # Generate realistic rating (weighted towards positive)
                    rating_weights = [1, 2, 8, 15, 20]  # Weights for ratings 1-5
                    rating = random.choices(range(1, 6), weights=rating_weights)[0]
                    
                    # Select appropriate review text
                    review_text = random.choice(self.review_templates[rating])
                    
                    # Create review document (removed voting fields for frontend compatibility)
                    review_doc = {
                        'recipe_id': recipe['original_id'] if 'original_id' in recipe else str(recipe['_id']),
                        'user_id': user_id,
                        'user_name': user['name'],
                        'rating': rating,
                        'review_text': review_text,
                        'created_at': datetime.utcnow() - timedelta(days=random.randint(1, 90)),
                        'updated_at': datetime.utcnow() - timedelta(days=random.randint(1, 90))
                    }
                    
                    if not dry_run:
                        try:
                            # Check if review already exists
                            existing = self.atlas_db.recipe_reviews.find_one({
                                'recipe_id': review_doc['recipe_id'],
                                'user_id': user_id
                            })
                            
                            if not existing:
                                self.atlas_db.recipe_reviews.insert_one(review_doc)
                                user_reviews_created += 1
                                reviews_created += 1

                                # Log every 100 reviews created
                                if reviews_created % 100 == 0:
                                    print(f"📝 Progress: {reviews_created} reviews created...")
                        except Exception as e:
                            print(f"⚠️  Error creating review: {e}")
                    else:
                        user_reviews_created += 1
                        reviews_created += 1
                
                if user_reviews_created > 0:
                    users_processed += 1
                    if not dry_run:
                        print(f"✅ Created {user_reviews_created} reviews for user {user['name']}")
                    else:
                        print(f"🔍 Would create {user_reviews_created} reviews for user {user['name']}")
            
            print(f"\n📊 SUMMARY:")
            print(f"   Reviews created: {reviews_created:,}")
            print(f"   Users processed: {users_processed:,}")
            
            self.recovery_stats['reviews_created'] = reviews_created
            self.recovery_stats['users_processed'] = users_processed
            
        except Exception as e:
            print(f"❌ Error creating balanced review system: {e}")

    def add_seed_reviews_for_popular_recipes(self, dry_run=True):
        """Add seed reviews for recipes that don't have any reviews yet."""
        print(f"\n🌱 {'[DRY RUN] ' if dry_run else ''}ADDING SEED REVIEWS FOR POPULAR RECIPES")
        print("=" * 50)
        
        try:
            # Get recipes without reviews
            if dry_run or 'recipe_reviews' not in self.atlas_db.list_collection_names():
                # In dry run or if no reviews exist, all recipes need seed reviews
                recipes_needing_reviews = list(self.atlas_db.recipes.find({}).limit(50))  # Limit to first 50
            else:
                # Find recipes without reviews
                reviewed_recipe_ids = set(self.atlas_db.recipe_reviews.distinct('recipe_id'))
                all_recipes = list(self.atlas_db.recipes.find({}))
                recipes_needing_reviews = [
                    r for r in all_recipes 
                    if (r.get('original_id', str(r['_id'])) not in reviewed_recipe_ids)
                ][:50]  # Limit to 50
            
            if not recipes_needing_reviews:
                print("✅ All recipes already have reviews!")
                return
            
            print(f"❄️  Found {len(recipes_needing_reviews)} recipes needing seed reviews")
            
            # Get some real users to attribute seed reviews to
            active_users = list(self.atlas_db.users.find({}).limit(20))
            
            if not active_users:
                print("❌ No users found to create seed reviews")
                return
            
            seed_reviews_added = 0
            
            for recipe in recipes_needing_reviews:
                recipe_id = recipe.get('original_id', str(recipe['_id']))
                
                # Add 2-4 seed reviews per recipe
                num_seed_reviews = random.randint(2, 4)
                
                # Select random users for this recipe
                recipe_reviewers = random.sample(active_users, min(num_seed_reviews, len(active_users)))
                
                for user in recipe_reviewers:
                    # Generate realistic rating distribution
                    rating = random.choices(range(1, 6), weights=[1, 3, 10, 20, 15])[0]
                    review_text = random.choice(self.review_templates[rating])
                    
                    seed_review = {
                        'recipe_id': recipe_id,
                        'user_id': str(user['_id']),
                        'user_name': user['name'],
                        'rating': rating,
                        'review_text': review_text,
                        'created_at': datetime.utcnow() - timedelta(days=random.randint(1, 30)),
                        'updated_at': datetime.utcnow() - timedelta(days=random.randint(1, 30))
                    }
                    
                    if not dry_run:
                        try:
                            # Check if this user already reviewed this recipe
                            existing = self.atlas_db.recipe_reviews.find_one({
                                'recipe_id': recipe_id,
                                'user_id': str(user['_id'])
                            })
                            
                            if not existing:
                                self.atlas_db.recipe_reviews.insert_one(seed_review)
                                seed_reviews_added += 1
                        except Exception as e:
                            print(f"⚠️  Error adding seed review: {e}")
                    else:
                        seed_reviews_added += 1
            
            print(f"🌱 {'Would add' if dry_run else 'Added'} {seed_reviews_added} seed reviews")
            
            if not dry_run:
                self.recovery_stats['recipes_enhanced'] = len(recipes_needing_reviews)
            
        except Exception as e:
            print(f"❌ Error adding seed reviews: {e}")

    def optimize_review_distribution(self, dry_run=True):
        """Optimize review distribution to ensure good recommendation quality."""
        print(f"\n⚖️ {'[DRY RUN] ' if dry_run else ''}OPTIMIZING REVIEW DISTRIBUTION")
        print("=" * 50)
        
        try:
            if dry_run or 'recipe_reviews' not in self.atlas_db.list_collection_names():
                print("🔍 Would analyze and optimize review distribution after creation")
                return
            
            # Analyze current distribution
            pipeline = [
                {'$group': {
                    '_id': '$recipe_id',
                    'review_count': {'$sum': 1},
                    'avg_rating': {'$avg': '$rating'},
                    'review_ids': {'$push': '$_id'}
                }},
                {'$sort': {'review_count': -1}}
            ]
            
            review_distribution = list(self.atlas_db.recipe_reviews.aggregate(pipeline))
            
            if not review_distribution:
                print("⚠️  No reviews found for optimization")
                return
            
            review_counts = [item['review_count'] for item in review_distribution]
            mean_reviews = statistics.mean(review_counts)
            
            print(f"📊 Current review distribution:")
            print(f"   Total recipes with reviews: {len(review_distribution)}")
            print(f"   Average reviews per recipe: {mean_reviews:.2f}")
            print(f"   Max reviews on single recipe: {max(review_counts)}")
            print(f"   Min reviews on single recipe: {min(review_counts)}")
            
            # Identify recipes with too many reviews (potential for cleanup)
            excessive_threshold = mean_reviews + 2 * statistics.stdev(review_counts) if len(review_counts) > 1 else mean_reviews * 2
            excessive_recipes = [item for item in review_distribution if item['review_count'] > excessive_threshold]
            
            if excessive_recipes:
                print(f"\n⚠️  Found {len(excessive_recipes)} recipes with excessive reviews (>{excessive_threshold:.0f})")
                # In a real scenario, you might want to remove some reviews from these recipes
            else:
                print("✅ Review distribution appears balanced")
            
        except Exception as e:
            print(f"❌ Error optimizing review distribution: {e}")

    def verify_frontend_integration(self):
        """Verify that generated reviews integrate properly with frontend."""
        print("\n🔍 VERIFYING FRONTEND INTEGRATION")
        print("=" * 50)

        try:
            if 'recipe_reviews' not in self.atlas_db.list_collection_names():
                print("❌ recipe_reviews collection not found")
                return False

            # Test sample of recipes to ensure they have reviews
            sample_recipes = list(self.atlas_db.recipes.find({}).limit(10))

            integration_issues = 0

            for recipe in sample_recipes:
                recipe_id = recipe.get('original_id', str(recipe['_id']))

                # Check if this recipe has reviews
                review_count = self.atlas_db.recipe_reviews.count_documents({'recipe_id': recipe_id})

                if review_count == 0:
                    print(f"⚠️  Recipe '{recipe.get('name', 'Unknown')}' (ID: {recipe_id}) has no reviews")
                    integration_issues += 1
                else:
                    # Get sample review to verify structure
                    sample_review = self.atlas_db.recipe_reviews.find_one({'recipe_id': recipe_id})

                    # Verify required fields for frontend
                    required_fields = ['recipe_id', 'user_id', 'user_name', 'rating', 'review_text', 'created_at', 'updated_at']
                    missing_fields = [field for field in required_fields if field not in sample_review]

                    if missing_fields:
                        print(f"❌ Recipe {recipe_id} review missing fields: {missing_fields}")
                        integration_issues += 1
                    else:
                        print(f"✅ Recipe '{recipe.get('name', 'Unknown')}' has {review_count} properly structured reviews")

            if integration_issues == 0:
                print("\n✅ All tested recipes have properly structured reviews for frontend integration")
                return True
            else:
                print(f"\n⚠️  Found {integration_issues} integration issues")
                return False

        except Exception as e:
            print(f"❌ Error verifying frontend integration: {e}")
            return False

    def generate_recovery_summary(self):
        """Generate summary of recovery and optimization results."""
        print("\n📋 RECOVERY & OPTIMIZATION SUMMARY")
        print("=" * 50)

        print(f"🗑️  Orphaned votes cleaned: {self.recovery_stats['orphaned_votes_cleaned']:,}")
        print(f"📝 Reviews created: {self.recovery_stats['reviews_created']:,}")
        print(f"👥 Users processed: {self.recovery_stats['users_processed']:,}")
        print(f"🍳 Recipes enhanced: {self.recovery_stats['recipes_enhanced']:,}")

        # Check final state
        if 'recipe_reviews' in self.atlas_db.list_collection_names():
            total_reviews = self.atlas_db.recipe_reviews.count_documents({})
            total_recipes = self.atlas_db.recipes.count_documents({})

            print(f"\n📊 FINAL STATE:")
            print(f"   Total reviews: {total_reviews:,}")
            print(f"   Total recipes: {total_recipes:,}")

            if total_recipes > 0:
                ratio = total_reviews / total_recipes
                print(f"   Review-to-recipe ratio: {ratio:.2f}")

                if 1 <= ratio <= 8:
                    print("✅ EXCELLENT: Optimal review-to-recipe ratio for recommendations")
                elif ratio > 8:
                    print("⚠️  HIGH: Many reviews per recipe - good for recommendations")
                else:
                    print("⚠️  LOW: Few reviews per recipe - may affect recommendation quality")

            # Verify frontend integration
            self.verify_frontend_integration()

    def cleanup_connections(self):
        """Clean up database connections."""
        try:
            if self.atlas_client:
                self.atlas_client.close()
        except Exception as e:
            print(f"⚠️  Error closing connections: {e}")

    def run_recovery_optimization(self, dry_run=True):
        """Run the complete recovery and optimization process."""
        print("🔧 SISARASA REVIEW DATA RECOVERY & OPTIMIZATION")
        print("=" * 60)
        print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🔍 Mode: {'DRY RUN (no changes will be made)' if dry_run else 'LIVE RUN (changes will be applied)'}")
        
        try:
            if not self.load_atlas_config():
                return False
            
            if not self.connect_database():
                return False
            
            # Create backup before making changes
            if not dry_run:
                backup_dir = self.backup_data()
                if not backup_dir:
                    print("❌ Backup failed - aborting recovery")
                    return False
            
            # Step 1: Clean orphaned votes
            self.clean_orphaned_votes(dry_run)
            
            # Step 2: Analyze user review expectations
            review_expectations = self.analyze_user_review_expectations()
            
            # Step 3: Create balanced review system
            if review_expectations:
                self.create_balanced_review_system(review_expectations, dry_run)
            
            # Step 4: Add seed reviews for recipes without reviews
            self.add_seed_reviews_for_popular_recipes(dry_run)
            
            # Step 5: Optimize review distribution
            self.optimize_review_distribution(dry_run)
            
            # Step 6: Generate summary
            self.generate_recovery_summary()
            
            print(f"\n⏰ Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            if dry_run:
                print("\n💡 This was a dry run. To apply changes, run with --apply flag")
            else:
                print("\n✅ Recovery and optimization completed successfully!")
                print("🎯 Your review system is now optimized for better recommendations!")
            
            return True
            
        except Exception as e:
            print(f"❌ Recovery and optimization failed: {e}")
            return False
        finally:
            self.cleanup_connections()


def main():
    """Main function to run the recovery and optimization."""
    print("SisaRasa Review Data Recovery & Optimizer")
    print("-" * 50)
    
    # Check for apply flag
    dry_run = '--apply' not in sys.argv
    
    if not dry_run:
        print("\n⚠️  WARNING: This will make permanent changes to your database!")
        print("This will:")
        print("  • Delete all orphaned review votes")
        print("  • Create a new recipe_reviews collection")
        print("  • Generate realistic review data based on user analytics")
        print("  • Add seed reviews for recipes without reviews")
        
        response = input("\nAre you sure you want to continue? (type 'RECOVER' to confirm): ")
        if response != 'RECOVER':
            print("Recovery cancelled.")
            return
    
    optimizer = ReviewRecoveryOptimizer()
    success = optimizer.run_recovery_optimization(dry_run=dry_run)
    
    if success:
        if dry_run:
            print("\n🔍 Dry run completed! Review the analysis above.")
            print("To apply changes, run: python recover_and_optimize_reviews.py --apply")
        else:
            print("\n🎉 Recovery and optimization completed successfully!")
            print("Your SisaRasa system now has a balanced review system for better recommendations.")
    else:
        print("\n❌ Recovery and optimization failed!")


if __name__ == "__main__":
    main()
