#!/usr/bin/env python3
"""
Comprehensive test for SisaRasa recipe recommendation functionality
"""

import os
import sys
import requests
import json
import time
from dotenv import load_dotenv

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_recipe_recommendations():
    """Test all recipe recommendation functionality."""
    print("🔍 Testing SisaRasa Recipe Recommendation System")
    print("=" * 60)
    
    # Load environment variables
    load_dotenv()
    
    # Test configuration
    base_url = "http://localhost:5000"
    
    # Test data
    test_ingredients = [
        ["egg", "rice", "tomato"],
        ["chicken", "onion", "garlic"],
        ["pasta", "cheese", "basil"],
        ["beef", "potato", "carrot"],
        ["fish", "lemon", "herbs"]
    ]
    
    print("🔄 Step 1: Testing system health...")
    
    try:
        # Test system health
        health_response = requests.get(f"{base_url}/api/health", timeout=10)
        print(f"📤 Health check status: {health_response.status_code}")
        
        if health_response.status_code == 200:
            health_data = health_response.json()
            print("✅ System health check passed")
            print(f"   Database: {health_data.get('components', {}).get('database', 'Unknown')}")
            print(f"   Recommender: {health_data.get('components', {}).get('recommender', 'Unknown')}")
        else:
            print(f"❌ Health check failed with status {health_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False
    
    print("\n🔄 Step 2: Testing ingredient autocomplete...")
    
    try:
        # Test ingredient autocomplete
        autocomplete_response = requests.get(
            f"{base_url}/api/ingredients?search=chick&limit=10",
            timeout=10
        )
        
        print(f"📤 Autocomplete response status: {autocomplete_response.status_code}")
        
        if autocomplete_response.status_code == 200:
            autocomplete_data = autocomplete_response.json()
            ingredients = autocomplete_data.get('ingredients', [])
            print(f"✅ Autocomplete working - found {len(ingredients)} ingredients")
            if ingredients:
                print(f"   Sample ingredients: {ingredients[:3]}")
        else:
            print(f"❌ Autocomplete failed with status {autocomplete_response.status_code}")
            
    except Exception as e:
        print(f"❌ Autocomplete error: {e}")
    
    print("\n🔄 Step 3: Testing recipe recommendations...")
    
    success_count = 0
    total_tests = len(test_ingredients)
    
    for i, ingredients in enumerate(test_ingredients, 1):
        print(f"\n   Test {i}/{total_tests}: {', '.join(ingredients)}")
        
        try:
            # Test recipe recommendation
            recommendation_data = {
                "ingredients": ingredients,
                "max_results": 5
            }
            
            recommendation_response = requests.post(
                f"{base_url}/api/recommend",
                json=recommendation_data,
                headers={'Content-Type': 'application/json'},
                timeout=15
            )
            
            print(f"   📤 Status: {recommendation_response.status_code}")
            
            if recommendation_response.status_code == 200:
                rec_data = recommendation_response.json()
                recipes = rec_data.get('recipes', [])
                search_ingredients = rec_data.get('search_ingredients', [])
                
                print(f"   ✅ Found {len(recipes)} recipes")
                print(f"   🔍 Processed ingredients: {len(search_ingredients)}")
                
                if recipes:
                    # Show sample recipe details
                    sample_recipe = recipes[0]
                    print(f"   📖 Top recipe: {sample_recipe.get('name', 'N/A')}")
                    print(f"   🍽️  Cuisine: {sample_recipe.get('cuisine', 'N/A')}")
                    print(f"   ⏱️  Prep time: {sample_recipe.get('prep_time', 'N/A')} min")
                    print(f"   👨‍🍳 Difficulty: {sample_recipe.get('difficulty', 'N/A')}")
                    
                    # Check if ingredients are decoded (not showing IDs)
                    recipe_ingredients = sample_recipe.get('ingredients', [])
                    if recipe_ingredients:
                        sample_ingredient = recipe_ingredients[0] if isinstance(recipe_ingredients, list) else str(recipe_ingredients)
                        if not any(x in str(sample_ingredient).lower() for x in ['_id', 'objectid', 'fruit_', 'protein_']):
                            print(f"   ✅ Ingredients properly decoded")
                        else:
                            print(f"   ⚠️  Ingredients may still show IDs: {sample_ingredient}")
                    
                    success_count += 1
                else:
                    print(f"   ⚠️  No recipes found for these ingredients")
            else:
                print(f"   ❌ Recommendation failed with status {recommendation_response.status_code}")
                try:
                    error_data = recommendation_response.json()
                    print(f"   Error: {error_data.get('message', 'Unknown error')}")
                except:
                    print(f"   Raw response: {recommendation_response.text[:200]}")
                    
        except Exception as e:
            print(f"   ❌ Recommendation error: {e}")
    
    print(f"\n📊 Recommendation Test Results: {success_count}/{total_tests} successful")
    
    print("\n🔄 Step 4: Testing community recipes...")
    
    try:
        # Test community recipes
        community_response = requests.get(f"{base_url}/api/community/recipes", timeout=10)
        
        print(f"📤 Community recipes status: {community_response.status_code}")
        
        if community_response.status_code == 200:
            community_data = community_response.json()
            recipes = community_data.get('recipes', [])
            print(f"✅ Community recipes accessible - {len(recipes)} recipes")
            
            if recipes:
                sample_recipe = recipes[0]
                print(f"   📖 Sample recipe: {sample_recipe.get('name', 'N/A')}")
        else:
            print(f"❌ Community recipes failed with status {community_response.status_code}")
            
    except Exception as e:
        print(f"❌ Community recipes error: {e}")
    
    print("\n🔄 Step 5: Testing web interface accessibility...")
    
    # Test key web pages
    pages_to_test = [
        ("/", "Welcome page"),
        ("/login", "Login page"),
        ("/signup", "Signup page"),
        ("/dashboard", "Dashboard"),
        ("/community-recipes", "Community recipes page")
    ]
    
    web_success = 0
    for path, name in pages_to_test:
        try:
            page_response = requests.get(f"{base_url}{path}", timeout=10)
            if page_response.status_code == 200:
                print(f"   ✅ {name}: Accessible")
                web_success += 1
            else:
                print(f"   ❌ {name}: Status {page_response.status_code}")
        except Exception as e:
            print(f"   ❌ {name}: Error {e}")
    
    print(f"\n📊 Web Interface Results: {web_success}/{len(pages_to_test)} pages accessible")
    
    # Final assessment
    print("\n" + "=" * 60)
    print("🎯 FINAL ASSESSMENT")
    print("=" * 60)
    
    overall_success = True
    
    if success_count >= total_tests * 0.8:  # 80% success rate
        print("✅ Recipe Recommendations: WORKING")
    else:
        print("❌ Recipe Recommendations: ISSUES DETECTED")
        overall_success = False
    
    if web_success >= len(pages_to_test) * 0.8:  # 80% success rate
        print("✅ Web Interface: WORKING")
    else:
        print("❌ Web Interface: ISSUES DETECTED")
        overall_success = False
    
    if overall_success:
        print("\n🎉 SisaRasa Recipe Recommendation System: FULLY OPERATIONAL")
        return True
    else:
        print("\n⚠️  SisaRasa Recipe Recommendation System: ISSUES DETECTED")
        return False

if __name__ == "__main__":
    success = test_recipe_recommendations()
    if success:
        print("\n✅ All tests PASSED - System ready for use!")
    else:
        print("\n❌ Some tests FAILED - Please check the issues above")
